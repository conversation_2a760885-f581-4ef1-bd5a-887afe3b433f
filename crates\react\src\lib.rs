pub use rink_core::hooks::state::{<PERSON><PERSON><PERSON><PERSON>, StateSetter, use_state};

/// Prelude module for common imports
pub mod prelude {
    pub use rink_core::{Renderable, VNode, render};
    pub use rink_core_macros::rsx;

    // Re-export commonly used Ratatui types for convenience
    pub use ratatui::{
        layout::{Alignment, Layout},
        style::{Color, Style},
        widgets::{Block, Borders, Paragraph},
    };
}
