pub use rink_core::hooks::event::{set_current_event_context, use_event};
pub use rink_core::hooks::state::{StateHandle, StateSetter, use_state};

/// Prelude module for common imports
pub mod prelude {
    pub use rink_core::{Renderable, VNode, render};
    pub use rink_core_macros::rsx;

    // Re-export commonly used Ratatui types for convenience
    pub use ratatui::{
        layout::{Alignment, Layout},
        style::{Color, Style},
        widgets::{Block, Borders, Paragraph},
    };
}
