[workspace]
resolver = "2"
members = ["crates/*"]

[workspace.dependencies]
chrono = "0.4.40"
crossbeam = "0.8"
crossterm = "0.29"
once_cell = "1.21"
parking_lot = "0.12.4"
rand = "0.9.0"
ratatui = "0.29"
reqwest = { version = "0.12.21" }
serde = { version = "1.0" }
serde_json = "1.0"
crossbeam-channel = "0.5.8"
tokio = { version = "1.45.1", features = ["full"] }

better-panic = "0.3.0"
human-panic = "2.0.2"
thiserror = "2.0.12"

tracing = "0.1"
tracing-futures = "0.2"
tracing-subscriber = { version = "0.3", default-features = false }
tracing-appender = "0.2"

proc-macro2 = "1"
proc-macro2-diagnostics = "0.10"
quote = "1.0.40"
syn = "2.0.104"
trybuild = "1.0.105"

rink = { path = "crates/react" }
rink_core = { path = "crates/core" }
rink_core_macros = { path = "crates/core-macros" }
