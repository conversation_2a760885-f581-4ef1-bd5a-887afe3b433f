use parking_lot::Mutex;
use std::any::Any;
use std::sync::Arc;

#[cfg(test)]
mod tests;

use crate::hooks::with_hook_context;
use crate::panic_handler::spawn_catch_panic;

/// Trait for types that can be used as effect dependencies
/// This enables dependency comparison for conditional effect re-execution
pub trait EffectDependencies: Any + Send + Sync {
    /// Compare this dependency set with another for equality
    fn deps_eq(&self, other: &dyn EffectDependencies) -> bool;

    /// Clone this dependency set as a boxed trait object
    fn clone_deps(&self) -> Box<dyn EffectDependencies>;

    /// Get a debug representation of the dependencies
    fn debug_deps(&self) -> String;
}

// Add as_any method to EffectDependencies trait
impl dyn EffectDependencies {
    pub fn as_any(&self) -> &dyn Any {
        self
    }
}

// Implement EffectDependencies for common types:

// Implement EffectDependencies for unit type () - represents empty dependencies
impl EffectDependencies for () {
    fn deps_eq(&self, other: &dyn EffectDependencies) -> bool {
        other.as_any().downcast_ref::<()>().is_some()
    }

    fn clone_deps(&self) -> Box<dyn EffectDependencies> {
        Box::new(())
    }

    fn debug_deps(&self) -> String {
        "()".to_string()
    }
}

// Implement for primitive types
macro_rules! impl_effect_deps_for_primitive {
    ($($t:ty),*) => {
        $(
            impl EffectDependencies for $t {
                fn deps_eq(&self, other: &dyn EffectDependencies) -> bool {
                    if let Some(other_val) = other.as_any().downcast_ref::<$t>() {
                        self == other_val
                    } else {
                        false
                    }
                }

                fn clone_deps(&self) -> Box<dyn EffectDependencies> {
                    Box::new(*self)
                }

                fn debug_deps(&self) -> String {
                    format!("{:?}", self)
                }
            }
        )*
    };
}

impl_effect_deps_for_primitive!(
    i8, i16, i32, i64, i128, isize, u8, u16, u32, u64, u128, usize, f32, f64, bool, char
);

impl EffectDependencies for String {
    fn deps_eq(&self, other: &dyn EffectDependencies) -> bool {
        if let Some(other_val) = other.as_any().downcast_ref::<String>() {
            self == other_val
        } else {
            false
        }
    }

    fn clone_deps(&self) -> Box<dyn EffectDependencies> {
        Box::new(self.clone())
    }

    fn debug_deps(&self) -> String {
        format!("{:?}", self)
    }
}

impl EffectDependencies for &'static str {
    fn deps_eq(&self, other: &dyn EffectDependencies) -> bool {
        if let Some(other_val) = other.as_any().downcast_ref::<&'static str>() {
            self == other_val
        } else {
            false
        }
    }

    fn clone_deps(&self) -> Box<dyn EffectDependencies> {
        Box::new(*self)
    }

    fn debug_deps(&self) -> String {
        format!("{:?}", self)
    }
}

/// Thread-safe wrapper for synchronous cleanup functions
/// Ensures cleanup functions can be safely called from any thread
pub struct CleanupFn {
    /// The actual cleanup function, wrapped in Arc<Mutex<>> for thread safety
    #[allow(clippy::type_complexity)]
    cleanup: Arc<Mutex<Option<Box<dyn FnOnce() + Send + 'static>>>>,
}

/// Thread-safe wrapper for asynchronous cleanup functions
/// Ensures async cleanup functions can be safely called from any thread
pub struct AsyncCleanupFn {
    /// The actual async cleanup function, wrapped in Arc<Mutex<>> for thread safety
    #[allow(clippy::type_complexity)]
    cleanup: Arc<
        Mutex<
            Option<
                Box<
                    dyn FnOnce() -> std::pin::Pin<
                            Box<dyn std::future::Future<Output = ()> + Send + 'static>,
                        > + Send
                        + 'static,
                >,
            >,
        >,
    >,
}

impl CleanupFn {
    /// Create a new cleanup function wrapper
    pub fn new<F>(cleanup: F) -> Self
    where
        F: FnOnce() + Send + 'static,
    {
        Self {
            cleanup: Arc::new(Mutex::new(Some(Box::new(cleanup)))),
        }
    }

    /// Execute the cleanup function if it hasn't been called yet
    /// This is idempotent - calling it multiple times is safe
    pub fn cleanup(&self) {
        if let Some(cleanup_fn) = self.cleanup.lock().take() {
            cleanup_fn();
        }
    }
}

impl Clone for CleanupFn {
    fn clone(&self) -> Self {
        Self {
            cleanup: self.cleanup.clone(),
        }
    }
}

impl AsyncCleanupFn {
    /// Create a new async cleanup function wrapper
    pub fn new<F, Fut>(cleanup: F) -> Self
    where
        F: FnOnce() -> Fut + Send + 'static,
        Fut: std::future::Future<Output = ()> + Send + 'static,
    {
        Self {
            cleanup: Arc::new(Mutex::new(Some(Box::new(move || {
                Box::pin(cleanup())
                    as std::pin::Pin<Box<dyn std::future::Future<Output = ()> + Send + 'static>>
            })))),
        }
    }

    /// Execute the async cleanup function if it hasn't been called yet
    /// This is idempotent - calling it multiple times is safe
    pub async fn cleanup(&self) {
        let cleanup_fn = self.cleanup.lock().take();
        if let Some(cleanup_fn) = cleanup_fn {
            cleanup_fn().await;
        }
    }
}

impl Clone for AsyncCleanupFn {
    fn clone(&self) -> Self {
        Self {
            cleanup: self.cleanup.clone(),
        }
    }
}

/// Internal state for tracking synchronous effects
struct EffectState {
    /// Previous dependencies for comparison
    prev_deps: Option<Box<dyn EffectDependencies>>,
    /// Cleanup function from the previous effect run
    cleanup: Option<CleanupFn>,
    /// Whether this effect has been initialized
    initialized: bool,
}

impl EffectState {
    fn new() -> Self {
        Self {
            prev_deps: None,
            cleanup: None,
            initialized: false,
        }
    }
}

/// Internal state for tracking asynchronous effects
struct AsyncEffectState {
    /// Previous dependencies for comparison
    prev_deps: Option<Box<dyn EffectDependencies>>,
    /// Async cleanup function from the previous effect run
    cleanup: Option<AsyncCleanupFn>,
    /// Whether this effect has been initialized
    initialized: bool,
}

impl AsyncEffectState {
    fn new() -> Self {
        Self {
            prev_deps: None,
            cleanup: None,
            initialized: false,
        }
    }
}

// Implement EffectDependencies for tuples (up to 8 elements for practical use)
macro_rules! impl_effect_deps_for_tuple {
    ($($t:ident),*) => {
        impl<$($t: EffectDependencies + Clone + PartialEq + std::fmt::Debug + 'static),*> EffectDependencies for ($($t,)*) {
            fn deps_eq(&self, other: &dyn EffectDependencies) -> bool {
                if let Some(other_tuple) = other.as_any().downcast_ref::<($($t,)*)>() {
                    self == other_tuple
                } else {
                    false
                }
            }

            fn clone_deps(&self) -> Box<dyn EffectDependencies> {
                Box::new(self.clone())
            }

            fn debug_deps(&self) -> String {
                format!("{:?}", self)
            }
        }
    };
}

// Implement for tuples of various sizes
impl_effect_deps_for_tuple!(T1);
impl_effect_deps_for_tuple!(T1, T2);
impl_effect_deps_for_tuple!(T1, T2, T3);
impl_effect_deps_for_tuple!(T1, T2, T3, T4);
impl_effect_deps_for_tuple!(T1, T2, T3, T4, T5);
impl_effect_deps_for_tuple!(T1, T2, T3, T4, T5, T6);
impl_effect_deps_for_tuple!(T1, T2, T3, T4, T5, T6, T7);
impl_effect_deps_for_tuple!(T1, T2, T3, T4, T5, T6, T7, T8);

// Implement EffectDependencies for Option<T> where T: EffectDependencies
impl<T> EffectDependencies for Option<T>
where
    T: EffectDependencies + Clone + PartialEq + std::fmt::Debug + 'static,
{
    fn deps_eq(&self, other: &dyn EffectDependencies) -> bool {
        if let Some(other_option) = other.as_any().downcast_ref::<Option<T>>() {
            match (self, other_option) {
                (None, None) => true,
                (Some(a), Some(b)) => a.deps_eq(b),
                _ => false,
            }
        } else {
            false
        }
    }

    fn clone_deps(&self) -> Box<dyn EffectDependencies> {
        Box::new(self.clone())
    }

    fn debug_deps(&self) -> String {
        match self {
            None => "None".to_string(),
            Some(value) => format!("Some({})", value.debug_deps()),
        }
    }
}

/// React-style useAsyncEffect hook that provides async side effect management for components
///
/// This function provides async effects with React's useEffect behavior:
/// - Async effects run after component render (not during)
/// - Supports dependency arrays for conditional re-execution
/// - Supports async cleanup functions returned from effects
/// - Handles effect cleanup on component unmount
/// - Supports empty dependency array for run-once effects
/// - Supports None dependencies for effects that run on every render
///
/// # Examples
///
/// ## Basic Async Effect (runs on every render)
/// ```rust,no_run
/// use rink_core::hooks::state::use_state;
/// use rink_core::hooks::effect::use_async_effect;
/// use rink_core::VNode;
///
/// fn my_component() -> VNode {
///     let (count, set_count) = use_state(0);
///     let count_value = count.get();
///
///     // Async effect runs on every render
///     use_async_effect::<(), _, _, _, _>(move || async move {
///         println!("Component rendered with count: {}", count_value);
///         None::<fn() -> std::pin::Pin<Box<dyn std::future::Future<Output = ()> + Send>>> // No cleanup needed
///     }, None);
///
///     VNode::Text(format!("Count: {}", count_value))
/// }
/// ```
///
/// ## Async Effect with Dependencies
/// ```rust,no_run
/// use rink_core::hooks::state::use_state;
/// use rink_core::hooks::effect::use_async_effect;
/// use rink_core::VNode;
///
/// fn user_profile() -> VNode {
///     let (user_id, _) = use_state(123);
///     let (user_data, set_user_data) = use_state(None::<String>);
///     let user_id_value = user_id.get();
///
///     // Async effect runs only when user_id changes
///     use_async_effect(move || async move {
///         // Simulate fetching user data asynchronously
///         let data = format!("User data for ID: {}", user_id_value);
///         set_user_data.set(Some(data));
///         None::<fn() -> std::pin::Pin<Box<dyn std::future::Future<Output = ()> + Send>>> // No cleanup needed
///     }, user_id_value);
///
///     VNode::Text(format!("User: {:?}", user_data.get()))
/// }
/// ```
///
/// ## Async Effect with Cleanup
/// ```rust,no_run
/// use rink_core::hooks::state::use_state;
/// use rink_core::hooks::effect::use_async_effect;
/// use rink_core::VNode;
///
/// fn timer() -> VNode {
///     let (seconds, set_seconds) = use_state(0);
///
///     // Async effect with cleanup function
///     use_async_effect(move || async move {
///         // Simulate async timer setup
///         println!("Timer started");
///
///         // Return async cleanup function
///         Some(|| Box::pin(async move {
///             println!("Timer cleanup");
///         }) as std::pin::Pin<Box<dyn std::future::Future<Output = ()> + Send>>)
///     }, ()); // Empty deps = run once
///
///     VNode::Text(format!("Timer: {} seconds", seconds.get()))
/// }
/// ```
///
/// # Async Support
///
/// The useAsyncEffect hook provides native async support:
///
/// ```rust,no_run
/// use rink_core::hooks::state::use_state;
/// use rink_core::hooks::effect::use_async_effect;
/// use rink_core::VNode;
///
/// fn async_component() -> VNode {
///     let (data, set_data) = use_state(None::<String>);
///
///     // Native async effect
///     use_async_effect(move || async move {
///         // Simulate async data fetching
///         let result = "Fetched data".to_string();
///         set_data.set(Some(result));
///         None::<fn() -> std::pin::Pin<Box<dyn std::future::Future<Output = ()> + Send>>>
///     }, ());
///
///     VNode::Text(format!("Data: {:?}", data.get()))
/// }
/// ```
///
/// # Error Handling
///
/// This function will panic if called outside of a component render context.
/// Always ensure useAsyncEffect is called within a component function.
///
/// # Performance Notes
///
/// - Async effects are scheduled to run after render, not during
/// - Dependency comparison uses PartialEq for efficient change detection
/// - Async cleanup functions are automatically managed and called when needed
/// - Multiple effects in the same component are executed in declaration order
pub fn use_async_effect<Deps, F, Fut, C, CFut>(effect: F, deps: impl Into<Option<Deps>>)
where
    Deps: EffectDependencies + Clone + PartialEq + 'static,
    F: FnOnce() -> Fut + Send + 'static,
    Fut: std::future::Future<Output = Option<C>> + Send + 'static,
    C: FnOnce() -> CFut + Send + 'static,
    CFut: std::future::Future<Output = ()> + Send + 'static,
{
    let deps = deps.into();
    with_hook_context(|ctx| {
        let hook_index = ctx.next_hook_index();
        let mut states = ctx.states.borrow_mut();

        // Get or create async effect state for this hook
        let effect_state = states
            .entry(hook_index)
            .or_insert_with(|| Box::new(AsyncEffectState::new()))
            .downcast_mut::<AsyncEffectState>()
            .expect("Async effect state type mismatch");

        // Determine if effect should run
        let should_run = match &deps {
            None => {
                // No dependencies - run on every render
                true
            }
            Some(current_deps) => {
                // Check if dependencies have changed
                match &effect_state.prev_deps {
                    None => {
                        // First run - always execute
                        true
                    }
                    Some(prev_deps) => {
                        // Compare dependencies
                        !current_deps.deps_eq(prev_deps.as_ref())
                    }
                }
            }
        };

        if should_run {
            // Run cleanup from previous effect if it exists
            if let Some(cleanup) = effect_state.cleanup.take() {
                // For now, we'll spawn the cleanup to run asynchronously
                // In a real implementation, this would be properly scheduled
                tokio::spawn(spawn_catch_panic(async move {
                    cleanup.cleanup().await;
                }));
            }

            // Store new dependencies
            if let Some(current_deps) = &deps {
                effect_state.prev_deps = Some(current_deps.clone_deps());
            } else {
                effect_state.prev_deps = None;
            }

            // Schedule async effect to run after render
            // For now, we'll spawn it immediately, but in a real implementation
            // this would be scheduled to run after the render phase
            tokio::spawn(spawn_catch_panic(async move {
                if let Some(cleanup_fn) = effect().await {
                    // Store the cleanup function for later use
                    // In a real implementation, we'd need to store this back in the state
                    let _async_cleanup = AsyncCleanupFn::new(cleanup_fn);
                    // TODO: Store this cleanup function in the effect state
                }
            }));

            effect_state.initialized = true;
        }
    });
}

/// React-style useEffect hook that provides synchronous side effect management for components
///
/// This function exactly mirrors React's useEffect behavior for synchronous effects:
/// - Effects run after component render (not during)
/// - Supports dependency arrays for conditional re-execution
/// - Supports cleanup functions returned from effects
/// - Handles effect cleanup on component unmount
/// - Supports empty dependency array for run-once effects
/// - Supports None dependencies for effects that run on every render
///
/// # Examples
///
/// ## Basic Sync Effect (runs on every render)
/// ```rust,no_run
/// use rink_core::hooks::state::use_state;
/// use rink_core::hooks::effect::use_effect;
/// use rink_core::VNode;
///
/// fn my_component() -> VNode {
///     let (count, set_count) = use_state(0);
///     let count_value = count.get();
///
///     // Sync effect runs on every render
///     use_effect::<(), _, _>(move || {
///         println!("Component rendered with count: {}", count_value);
///         None::<Box<dyn FnOnce() + Send>> // No cleanup needed
///     }, None);
///
///     VNode::Text(format!("Count: {}", count_value))
/// }
/// ```
///
/// ## Sync Effect with Dependencies
/// ```rust,no_run
/// use rink_core::hooks::state::use_state;
/// use rink_core::hooks::effect::use_effect;
/// use rink_core::VNode;
///
/// fn user_profile() -> VNode {
///     let (user_id, _) = use_state(123);
///     let (user_data, set_user_data) = use_state(None::<String>);
///     let user_id_value = user_id.get();
///
///     // Sync effect runs only when user_id changes
///     use_effect(move || {
///         // Simulate fetching user data synchronously
///         let data = format!("User data for ID: {}", user_id_value);
///         set_user_data.set(Some(data));
///         None::<Box<dyn FnOnce() + Send>> // No cleanup needed
///     }, user_id_value);
///
///     VNode::Text(format!("User: {:?}", user_data.get()))
/// }
/// ```
///
/// ## Sync Effect with Cleanup
/// ```rust,no_run
/// use rink_core::hooks::state::use_state;
/// use rink_core::hooks::effect::use_effect;
/// use rink_core::VNode;
///
/// fn timer() -> VNode {
///     let (seconds, set_seconds) = use_state(0);
///
///     // Sync effect with cleanup function
///     use_effect(move || {
///         // Simulate timer setup
///         println!("Timer started");
///
///         // Return cleanup function
///         Some(Box::new(move || {
///             println!("Timer cleanup");
///         }) as Box<dyn FnOnce() + Send>)
///     }, ()); // Empty deps = run once
///
///     VNode::Text(format!("Timer: {} seconds", seconds.get()))
/// }
/// ```
///
/// # Error Handling
///
/// This function will panic if called outside of a component render context.
/// Always ensure useEffect is called within a component function.
///
/// # Performance Notes
///
/// - Effects are scheduled to run after render, not during
/// - Dependency comparison uses PartialEq for efficient change detection
/// - Cleanup functions are automatically managed and called when needed
/// - Multiple effects in the same component are executed in declaration order
pub fn use_effect<Deps, F, C>(effect: F, deps: impl Into<Option<Deps>>)
where
    Deps: EffectDependencies + Clone + PartialEq + 'static,
    F: FnOnce() -> Option<C> + 'static,
    C: FnOnce() + Send + 'static,
{
    let deps = deps.into();
    with_hook_context(|ctx| {
        let hook_index = ctx.next_hook_index();
        let mut states = ctx.states.borrow_mut();

        // Get or create effect state for this hook
        let effect_state = states
            .entry(hook_index)
            .or_insert_with(|| Box::new(EffectState::new()))
            .downcast_mut::<EffectState>()
            .expect("Effect state type mismatch");

        // Determine if effect should run
        let should_run = match &deps {
            None => {
                // No dependencies - run on every render
                true
            }
            Some(current_deps) => {
                // Check if dependencies have changed
                match &effect_state.prev_deps {
                    None => {
                        // First run - always execute
                        true
                    }
                    Some(prev_deps) => {
                        // Compare dependencies
                        !current_deps.deps_eq(prev_deps.as_ref())
                    }
                }
            }
        };

        if should_run {
            // Run cleanup from previous effect if it exists
            if let Some(cleanup) = effect_state.cleanup.take() {
                cleanup.cleanup();
            }

            // Store new dependencies
            if let Some(current_deps) = &deps {
                effect_state.prev_deps = Some(current_deps.clone_deps());
            } else {
                effect_state.prev_deps = None;
            }

            // Schedule effect to run after render
            // For now, we'll run it immediately, but in a real implementation
            // this would be scheduled to run after the render phase
            if let Some(cleanup_fn) = effect() {
                effect_state.cleanup = Some(CleanupFn::new(cleanup_fn));
            }

            effect_state.initialized = true;
        }
    });
}
