use ratatui::{
    layout::{Constraint, Direction},
    style::{Color, Style},
    widgets::Borders,
};
use rink::prelude::*;

/// Dashboard layout with multiple widgets demonstrating RSX capabilities
fn build_dashboard() -> impl Renderable {
    rsx! {
        <Block
            title="System Dashboard"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Cyan)}
        >
            <Layout
                direction={Direction::Vertical}
                constraints={vec![
                    Constraint::Length(3),
                    Constraint::Min(0),
                    Constraint::Length(3)
                ]}
            >
                // Header section
                <Block
                    title="Status"
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Green)}
                >
                    <Paragraph
                        style={Style::default().fg(Color::White)}
                        alignment={Alignment::Center}
                    >
                        {"System Online | CPU: 45% | Memory: 2.1GB/8GB | Uptime: 5d 12h"}
                    </Paragraph>
                </Block>

                // Main content area
                <Layout
                    direction={Direction::Horizontal}
                    constraints={vec![
                        Constraint::Percentage(50),
                        Constraint::Percentage(50)
                    ]}
                >
                    // Left panel
                    <Layout
                        direction={Direction::Vertical}
                        constraints={vec![
                            Constraint::Percentage(60),
                            Constraint::Percentage(40)
                        ]}
                    >
                        // CPU Usage Chart
                        <Block
                            title="CPU Usage"
                            borders={Borders::ALL}
                            border_style={Style::default().fg(Color::Yellow)}
                        >
                            <Paragraph
                                style={Style::default().fg(Color::Yellow)}
                            >
                                {"CPU Usage: 45%\n████████████░░░░░░░░░░░░░░░░░░░░"}
                            </Paragraph>
                        </Block>

                        // Memory Usage
                        <Block
                            title="Memory Usage"
                            borders={Borders::ALL}
                            border_style={Style::default().fg(Color::Blue)}
                        >
                            <Paragraph
                                style={Style::default().fg(Color::Blue)}
                            >
                                {"Memory: 2.1GB/8GB (26%)\n████████░░░░░░░░░░░░░░░░░░░░░░░░"}
                            </Paragraph>
                        </Block>
                    </Layout>

                    // Right panel
                    <Layout
                        direction={Direction::Vertical}
                        constraints={vec![
                            Constraint::Percentage(50),
                            Constraint::Percentage(50)
                        ]}
                    >
                        // Process List
                        <Block
                            title="Top Processes"
                            borders={Borders::ALL}
                            border_style={Style::default().fg(Color::Magenta)}
                        >
                            <Paragraph
                                style={Style::default().fg(Color::Magenta)}
                            >
                                {"• chrome.exe - 15.2% CPU\n• node.exe - 8.7% CPU\n• rust-analyzer - 5.1% CPU\n• firefox.exe - 4.3% CPU\n• code.exe - 3.8% CPU"}
                            </Paragraph>
                        </Block>

                        // Network Activity
                        <Block
                            title="Network Activity"
                            borders={Borders::ALL}
                            border_style={Style::default().fg(Color::Red)}
                        >
                            <Paragraph
                                style={Style::default().fg(Color::White)}
                            >
                                {"↑ Upload: 1.2 MB/s"}
                                {"\n"}
                                {"↓ Download: 5.8 MB/s"}
                                {"\n"}
                                {"📡 Connections: 47 active"}
                                {"\n"}
                                {"🌐 Packets: 1,247 sent, 3,891 received"}
                            </Paragraph>
                        </Block>
                    </Layout>
                </Layout>

                // Footer section
                <Block
                    title="System Info"
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Gray)}
                >
                    <Paragraph
                        style={Style::default().fg(Color::DarkGray)}
                        alignment={Alignment::Center}
                    >
                        {"OS: Windows 11 | Kernel: 10.0.22631 | Architecture: x64 | Last Updated: 2025-01-02 14:30:15"}
                    </Paragraph>
                </Block>
            </Layout>
        </Block>
    }
}

/// Entry point for the dashboard demo
fn main() -> Result<(), Box<dyn std::error::Error>> {
    let dashboard = build_dashboard();
    render(dashboard)
}
