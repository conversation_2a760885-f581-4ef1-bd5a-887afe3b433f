//! Professional interval hook implementation for periodic execution
//!
//! This module provides the `use_interval` hook for executing callbacks at regular intervals,
//! similar to JavaScript's `setInterval` but with proper Rust async integration and cleanup.
//!
//! ## Key Features:
//! - Automatic cleanup when component unmounts or dependencies change
//! - Proper async/await integration with tokio runtime
//! - Thread-safe execution with proper error handling
//! - Professional resource management and memory safety
//! - Integration with the hook lifecycle system
//!
//! ## Usage Examples:
//!
//! ### Basic Interval (runs every second)
//! ```rust,no_run
//! use rink_core::hooks::interval::use_interval;
//! use rink_core::VNode;
//! use std::time::Duration;
//!
//! fn my_component() -> VNode {
//!     // Execute callback every second
//!     use_interval(|| {
//!         println!("This runs every second!");
//!     }, Duration::from_secs(1));
//!
//!     VNode::Text("Timer component".to_string())
//! }
//! ```
//!
//! ### Interval with State Updates
//! ```rust,no_run
//! use rink_core::hooks::state::use_state;
//! use rink_core::hooks::interval::use_interval;
//! use rink_core::VNode;
//! use std::time::Duration;
//!
//! fn counter_component() -> VNode {
//!     let (count, set_count) = use_state(0);
//!
//!     // Increment counter every 500ms
//!     use_interval(move || {
//!         set_count.update(|prev| prev + 1);
//!     }, Duration::from_millis(500));
//!
//!     VNode::Text(format!("Count: {}", count.get()))
//! }
//! ```

use std::time::Duration;

#[cfg(test)]
mod tests;

use crate::hooks::effect::EffectDependencies;

// Implement EffectDependencies for Duration to enable dependency tracking
impl EffectDependencies for Duration {
    fn deps_eq(&self, other: &dyn EffectDependencies) -> bool {
        if let Some(other_duration) = other.as_any().downcast_ref::<Duration>() {
            self == other_duration
        } else {
            false
        }
    }

    fn clone_deps(&self) -> Box<dyn EffectDependencies> {
        Box::new(*self)
    }

    fn debug_deps(&self) -> String {
        format!("{:?}", self)
    }
}

/// Professional interval hook for periodic callback execution
///
/// This hook provides a React-like `setInterval` functionality with proper cleanup
/// and integration with the component lifecycle. The interval automatically starts
/// when the component mounts and stops when it unmounts.
///
/// ## Key Features:
/// - Automatic cleanup on component unmount
/// - Thread-safe execution with proper error handling
/// - Integration with tokio async runtime
/// - Professional resource management
/// - Consistent timing with tokio::time::interval
///
/// ## Parameters:
/// - `callback`: Function to execute at each interval
/// - `duration`: Time between executions
///
/// ## Behavior:
/// - The interval starts immediately when the hook is called
/// - If the duration changes, the interval is restarted with the new duration
/// - The interval is automatically cancelled when the component unmounts
/// - All spawned tasks are properly cleaned up to prevent memory leaks
///
/// ## Thread Safety:
/// The callback must be `Send + 'static` to ensure thread safety across async boundaries.
/// State updates should use thread-safe mechanisms like the state hooks.
///
/// ## Performance:
/// Uses tokio's optimized interval timer for accurate timing with minimal overhead.
/// The implementation avoids busy-waiting and integrates with the async runtime efficiently.
pub fn use_interval<F>(callback: F, duration: Duration)
where
    F: Fn() + Send + 'static,
{
    use crate::hooks::effect::use_effect;
    use tokio::time::interval;

    // Use effect to manage the interval lifecycle with proper cleanup
    use_effect(
        move || {
            // Handle zero duration by using minimum duration
            let safe_duration = if duration.is_zero() {
                Duration::from_millis(1)
            } else {
                duration
            };

            // Spawn interval task
            let handle = tokio::spawn(async move {
                let mut interval_timer = interval(safe_duration);

                loop {
                    interval_timer.tick().await;
                    callback();
                }
            });

            // Return cleanup function that cancels the task
            Some(Box::new(move || {
                handle.abort();
            }) as Box<dyn FnOnce() + Send>)
        },
        duration, // Effect depends on duration - restarts when duration changes
    );
}
