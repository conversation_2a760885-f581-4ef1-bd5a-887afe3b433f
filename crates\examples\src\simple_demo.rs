use rink::prelude::*;

/// Constructs the UI tree declaratively using rsx!
fn build_ui() -> impl Renderable {
    rsx! {
        <Block
            title="Hello World"
            borders={Borders::ALL}
            border_style={Style::default().fg(Color::Cyan)}
        >
            <Paragraph
                style={Style::default().fg(Color::White)}
                alignment={Alignment::Center}
            >
                {"Welcome to Terminal React UI!"}
            </Paragraph>
        </Block>
    }
}

/// Entry point of the application
fn main() -> Result<(), Box<dyn std::error::Error>> {
    let ui = build_ui();
    render(ui)
}
