/*!
# Professional System Dashboard with use_state Hook Integration

This comprehensive example demonstrates the professional integration of the `use_state` hook
from the rink-leptos framework into a complex TUI application. It showcases both traditional
state management and modern hook-based reactive state management patterns.

## Key Features Implemented:

### 🎯 Hook-Based State Management
- **React-like `use_state` hook**: Complete integration with `use_state(initial_value)`
- **Professional component architecture**: Functional components with hook-based state
- **Thread-safe state access**: Arc<StateContainer<T>> with RwLock for concurrent access
- **Efficient field access**: `state.field(|s| s.field_name)` for minimal cloning
- **Hook context management**: Proper lifecycle management with HookContext

### 🏗️ Architecture Patterns
- **Dual implementation approach**: Both hook-based and traditional implementations
- **Component separation**: Clear separation between UI logic and state management
- **Professional error handling**: Type-safe state access with clear error messages
- **Memory efficiency**: Arc-based sharing without unnecessary deep cloning

### 📊 Complex State Structures
- **Nested state management**: Complex `AppState` with multiple sub-structures
- **Real-time data simulation**: System metrics with historical tracking
- **Multi-view dashboard**: Different views with state-driven UI rendering
- **Professional UI components**: RSX-based components with state integration

### 🔧 Professional Development Patterns
- **Comprehensive documentation**: Detailed examples and usage patterns
- **Type safety**: Full Rust type system integration with hooks
- **Performance optimization**: Efficient rendering and state updates
- **Scalable architecture**: Supports complex component hierarchies

## Usage:

Run the application and choose between:
1. **Hook-based implementation** (Recommended) - Uses `use_state` for reactive state management
2. **Traditional implementation** (Legacy) - Uses manual state management

## Components Demonstrated:

- `dashboard_component()` - Main dashboard using `use_state`
- `counter_component()` - Simple counter demonstrating hook patterns
- `state_update_examples_component()` - Various state update patterns

## State Management Patterns:

```rust
// Initialize state
let (app_state, set_app_state) = use_state(AppState::new());

// Read state
let current_state = app_state.get();
let cpu_usage = app_state.field(|s| s.cpu_usage.current);

// Update state (ready for implementation)
set_app_state.set(new_state);
set_app_state.update(|prev| updated_state);
```

This example serves as a comprehensive reference for integrating `use_state` hooks
into professional TUI applications with the rink-leptos framework.
*/

use crossterm::{
    event::{
        self, DisableMouseCapture, EnableMouseCapture, Event, KeyCode, KeyEventKind, KeyModifiers,
    },
    execute,
    terminal::{EnterAlternateScreen, LeaveAlternateScreen, disable_raw_mode, enable_raw_mode},
};
use ratatui::{
    Terminal,
    backend::CrosstermBackend,
    layout::{Alignment, Constraint, Direction},
    style::{Color, Modifier, Style},
    widgets::Borders,
};
use rink::prelude::*;
use rink_core::hooks::event::{set_current_event_context, use_event};
use rink_core::hooks::state::{StateSetter, use_state};
use rink_core::hooks::{HookContext, clear_hook_context, set_hook_context};
use std::{
    collections::VecDeque,
    io,
    rc::Rc,
    sync::Arc,
    time::{Duration, Instant},
};

/// Professional System Monitoring Dashboard
///
/// This comprehensive example demonstrates the full capabilities of the rink-leptos
/// RSX framework for building complex terminal user interfaces. Features include:
///
/// - Real-time system monitoring with simulated data
/// - Multi-panel dashboard layout with responsive design
/// - Interactive navigation between different views
/// - Live data visualization with historical tracking
/// - Professional styling and user experience
/// - Comprehensive error handling and state management
/// - Professional use_state hook integration for reactive state management
///
/// Navigation:
/// - Tab/Shift+Tab: Navigate between panels
/// - 1-5: Switch between different dashboard views
/// - Space: Pause/Resume real-time updates
/// - R: Reset all metrics
/// - Q: Quit application
/// - H: Toggle between hook-based and traditional implementation
fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 Professional System Dashboard with use_state Hook Integration");
    println!("Choose implementation:");
    println!("1. Hook-based implementation (use_state) - Recommended");
    println!("2. Traditional implementation (legacy)");
    println!("Enter choice (1 or 2): ");

    let mut input = String::new();
    io::stdin().read_line(&mut input)?;
    let choice = input.trim();

    match choice {
        "1" => run_hook_based_dashboard(),
        "2" => run_traditional_dashboard(),
        _ => {
            println!("Invalid choice, running hook-based implementation by default...");
            run_hook_based_dashboard()
        }
    }
}

/// Run the hook-based dashboard implementation using use_state
fn run_hook_based_dashboard() -> Result<(), Box<dyn std::error::Error>> {
    println!("🎯 Starting Hook-Based Dashboard with use_state...");

    // Setup terminal with proper error handling
    enable_raw_mode()?;
    let mut stdout = io::stdout();
    execute!(stdout, EnterAlternateScreen, EnableMouseCapture)?;
    let backend = CrosstermBackend::new(stdout);
    let mut terminal = Terminal::new(backend)?;

    // Create hook context for the component system
    let hook_context = Rc::new(HookContext::new());
    set_hook_context(hook_context.clone());

    // Run the hook-based dashboard with proper error handling
    let res = run_hook_dashboard(&mut terminal, hook_context);

    // Cleanup and restore terminal state
    clear_hook_context();
    disable_raw_mode()?;
    execute!(
        terminal.backend_mut(),
        LeaveAlternateScreen,
        DisableMouseCapture
    )?;
    terminal.show_cursor()?;

    if let Err(err) = res {
        eprintln!("Hook-based dashboard error: {:?}", err);
    }

    Ok(())
}

/// Run the traditional dashboard implementation (legacy)
fn run_traditional_dashboard() -> Result<(), Box<dyn std::error::Error>> {
    println!("📊 Starting Traditional Dashboard (Legacy)...");

    // Setup terminal with proper error handling
    enable_raw_mode()?;
    let mut stdout = io::stdout();
    execute!(stdout, EnterAlternateScreen, EnableMouseCapture)?;
    let backend = CrosstermBackend::new(stdout);
    let mut terminal = Terminal::new(backend)?;

    // Create and run the comprehensive dashboard
    let app = SystemDashboard::new();
    let res = run_dashboard(&mut terminal, app);

    // Restore terminal state
    disable_raw_mode()?;
    execute!(
        terminal.backend_mut(),
        LeaveAlternateScreen,
        DisableMouseCapture
    )?;
    terminal.show_cursor()?;

    if let Err(err) = res {
        eprintln!("Traditional dashboard error: {:?}", err);
    }

    Ok(())
}

/// Professional Event Handler for Dashboard Component
///
/// This function demonstrates the professional integration of event handling
/// with state management using the use_event and use_state hooks together.
///
/// ## Key Features:
/// - Handles keyboard events for navigation and control
/// - Updates state through StateSetter for reactive UI updates
/// - Supports complex key combinations and modifiers
/// - Professional error handling and state validation
/// - Demonstrates React-like event handling patterns
///
/// ## Supported Events:
/// - Navigation: Tab/Shift+Tab for panel navigation
/// - View switching: Number keys 1-5 for different dashboard views
/// - Control: Space for pause/resume, R for reset, Q for quit
/// - Modifiers: Ctrl+key combinations for advanced actions
fn handle_dashboard_event(event: &crossterm::event::Event, set_app_state: &StateSetter<AppState>) {
    match event {
        crossterm::event::Event::Key(key_event) => {
            // Only handle key press events, ignore key release
            if key_event.kind != crossterm::event::KeyEventKind::Press {
                return;
            }

            match key_event.code {
                // Quit application
                crossterm::event::KeyCode::Char('q') | crossterm::event::KeyCode::Char('Q') => {
                    set_app_state.update(|prev| AppState {
                        should_quit: true,
                        ..prev
                    });
                }

                // Toggle pause/resume
                crossterm::event::KeyCode::Char(' ') => {
                    set_app_state.update(|prev| AppState {
                        is_paused: !prev.is_paused,
                        ..prev
                    });
                }

                // Reset metrics
                crossterm::event::KeyCode::Char('r') | crossterm::event::KeyCode::Char('R') => {
                    set_app_state.update(|prev| AppState {
                        cpu_usage: MetricHistory::new(100),
                        memory_usage: MetricHistory::new(100),
                        network_upload: MetricHistory::new(100),
                        network_download: MetricHistory::new(100),
                        disk_usage: MetricHistory::new(100),
                        performance_counters: PerformanceCounters::new(),
                        ..prev
                    });
                }

                // View switching with number keys
                crossterm::event::KeyCode::Char('1') => {
                    set_app_state.update(|prev| AppState {
                        current_view: DashboardView::Overview,
                        ..prev
                    });
                }
                crossterm::event::KeyCode::Char('2') => {
                    set_app_state.update(|prev| AppState {
                        current_view: DashboardView::Performance,
                        ..prev
                    });
                }
                crossterm::event::KeyCode::Char('3') => {
                    set_app_state.update(|prev| AppState {
                        current_view: DashboardView::Processes,
                        ..prev
                    });
                }
                crossterm::event::KeyCode::Char('4') => {
                    set_app_state.update(|prev| AppState {
                        current_view: DashboardView::Network,
                        ..prev
                    });
                }
                crossterm::event::KeyCode::Char('5') => {
                    set_app_state.update(|prev| AppState {
                        current_view: DashboardView::Storage,
                        ..prev
                    });
                }

                // Panel navigation with Tab
                crossterm::event::KeyCode::Tab => {
                    set_app_state.update(|prev| AppState {
                        selected_panel: (prev.selected_panel + 1) % 4,
                        ..prev
                    });
                }

                // Reverse panel navigation with Shift+Tab
                crossterm::event::KeyCode::BackTab => {
                    set_app_state.update(|prev| AppState {
                        selected_panel: if prev.selected_panel == 0 {
                            3
                        } else {
                            prev.selected_panel - 1
                        },
                        ..prev
                    });
                }

                // Handle other key events (can be extended)
                _ => {
                    // For demonstration, we could handle more complex key combinations here
                    // For example: Ctrl+C, Alt+key combinations, function keys, etc.
                }
            }
        }

        // Handle mouse events (future enhancement)
        crossterm::event::Event::Mouse(_mouse_event) => {
            // Mouse event handling can be implemented here
            // For example: clicking on panels, dragging, scrolling
        }

        // Handle resize events
        crossterm::event::Event::Resize(_width, _height) => {
            // Terminal resize handling can be implemented here
            // For example: adjusting layout constraints, recalculating positions
        }

        // Handle other event types
        _ => {
            // Other events like focus, paste, etc. can be handled here
        }
    }
}

/// Professional Dashboard Component using use_state hook
///
/// This component demonstrates the professional integration of the use_state hook
/// from the rink-leptos framework for reactive state management in TUI applications.
///
/// ## Key Features Demonstrated:
///
/// ### 1. React-like State Management
/// - Uses `use_state(AppState::new())` for centralized state management
/// - Provides automatic re-rendering when state changes (future implementation)
/// - Thread-safe state access with Arc<StateContainer<T>> under the hood
///
/// ### 2. Complex State Structures
/// - Manages nested state with `AppState` containing multiple sub-structures
/// - Efficient field access using `app_state.field(|s| s.field_name)`
/// - Clones only necessary data, not the entire state structure
///
/// ### 3. Professional State Access Patterns
/// ```rust
/// let (app_state, set_app_state) = use_state(AppState::new());
/// let current_state = app_state.get();  // Get full state snapshot
/// let cpu_usage = app_state.field(|s| s.cpu_usage.current);  // Efficient field access
/// ```
///
/// ### 4. Hook Context Integration
/// - Properly integrates with HookContext for component lifecycle management
/// - Supports multiple hooks per component with automatic indexing
/// - Thread-local storage for hook state persistence across renders
///
/// ### 5. Functional State Updates (Ready for Implementation)
/// ```rust
/// // Direct value updates
/// set_app_state.set(new_state);
///
/// // Functional updates for safe concurrent access
/// set_app_state.update(|prev_state| {
///     AppState {
///         cpu_usage: updated_cpu_usage,
///         ..prev_state
///     }
/// });
/// ```
///
/// ### 6. Professional Error Handling
/// - Panics with clear error messages when hooks are called outside component context
/// - Type-safe state access with compile-time guarantees
/// - Memory-safe concurrent access using RwLock and Mutex
///
/// ### 7. Performance Optimizations
/// - Uses Arc for efficient state sharing without deep cloning
/// - RwLock enables concurrent reads while maintaining write safety
/// - Version tracking for efficient change detection (future optimization)
///
/// ## Architecture Benefits:
///
/// - **Separation of Concerns**: UI logic separated from state management
/// - **Testability**: State can be easily mocked and tested independently
/// - **Maintainability**: Clear state flow and predictable updates
/// - **Scalability**: Hook system supports complex component hierarchies
/// - **Performance**: Minimal re-renders and efficient memory usage
///
/// ## Usage Pattern:
///
/// This component follows the React functional component pattern:
/// 1. Initialize state with `use_state(initial_value)`
/// 2. Extract values for rendering using `.get()` or `.field()`
/// 3. Render UI based on current state
/// 4. Update state through setter functions (event handlers)
/// 5. Framework handles re-rendering automatically
///
/// ## Integration with RSX:
///
/// The component seamlessly integrates with the RSX macro system,
/// providing a familiar React-like development experience while
/// leveraging Rust's type safety and performance characteristics.
fn dashboard_component() -> VNode {
    // Initialize application state using the use_state hook
    // This provides React-like state management with automatic re-rendering
    let (app_state, set_app_state) = use_state(AppState::new());

    // Use the event hook to handle terminal events professionally
    // This demonstrates the integration of use_event with use_state
    if let Some(event) = use_event() {
        handle_dashboard_event(&event, &set_app_state);
    }

    // Extract current values from state for rendering
    let current_state = app_state.get();
    let _should_quit = current_state.should_quit;
    let current_view = current_state.current_view;
    let selected_panel = current_state.selected_panel;
    let is_paused = current_state.is_paused;

    // Create individual state handles for different metrics using field access
    let cpu_usage = app_state.field(|s| s.cpu_usage.current);
    let memory_usage = app_state.field(|s| s.memory_usage.current);
    let network_upload = app_state.field(|s| s.network_upload.current);
    let network_download = app_state.field(|s| s.network_download.current);

    // Professional status text generation using state values
    let status_text = format!(
        "System Online | CPU: {:.1}% | Memory: {:.1}GB/{:.1}GB | Uptime: {} | Status: {}",
        cpu_usage,
        memory_usage * current_state.system_info.total_memory_gb / 100.0,
        current_state.system_info.total_memory_gb,
        format_uptime_duration(current_state.system_info.uptime),
        if is_paused { "⏸ PAUSED" } else { "▶ LIVE" }
    );

    // Generate process information text from state
    let processes_text = current_state
        .processes
        .iter()
        .take(5)
        .map(|p| format!("• {} - {:.1}% CPU", p.name, p.cpu_percent))
        .collect::<Vec<_>>()
        .join("\n");

    // Network activity information from state
    let network_text = format!(
        "↑ Upload: {:.1} MB/s\n↓ Download: {:.1} MB/s\n📡 Connections: {} active\n🌐 Packets: {} sent, {} received",
        network_upload,
        network_download,
        current_state.network_stats.active_connections,
        current_state.network_stats.packets_sent,
        current_state.network_stats.packets_received
    );

    // Footer with system information and controls
    let footer_text = format!(
        "OS: {} | Kernel: {} | Arch: {} | [1-5] Views | [Space] Pause | [R] Reset | [Q] Quit {}",
        current_state.system_info.os_name,
        current_state.system_info.kernel_version,
        current_state.system_info.architecture,
        if is_paused { "⏸ PAUSED" } else { "▶ LIVE" }
    );

    // Render the dashboard using RSX with state-driven UI
    match current_view {
        DashboardView::Overview => render_overview_with_state(
            &current_state,
            &status_text,
            &processes_text,
            &network_text,
            &footer_text,
            selected_panel,
        ),
        DashboardView::Performance => render_performance_with_state(&current_state),
        DashboardView::Processes => render_processes_with_state(&current_state),
        DashboardView::Network => render_network_with_state(&current_state),
        DashboardView::Storage => render_storage_with_state(&current_state),
    }
}

/// Helper function to format uptime duration
fn format_uptime_duration(uptime: Duration) -> String {
    let days = uptime.as_secs() / 86400;
    let hours = (uptime.as_secs() % 86400) / 3600;
    let minutes = (uptime.as_secs() % 3600) / 60;

    if days > 0 {
        format!("{}d {}h {}m", days, hours, minutes)
    } else if hours > 0 {
        format!("{}h {}m", hours, minutes)
    } else {
        format!("{}m", minutes)
    }
}

/// Application state structure for use with hooks
#[derive(Debug, Clone)]
struct AppState {
    // Application state
    should_quit: bool,
    current_view: DashboardView,
    selected_panel: usize,
    is_paused: bool,

    // Real-time data
    last_update: Instant,
    update_interval: Duration,

    // System metrics with historical data
    cpu_usage: MetricHistory,
    memory_usage: MetricHistory,
    network_upload: MetricHistory,
    network_download: MetricHistory,
    disk_usage: MetricHistory,

    // Process information
    processes: Vec<ProcessInfo>,

    // System information
    system_info: SystemInfo,

    // Network statistics
    network_stats: NetworkStats,

    // Performance counters
    performance_counters: PerformanceCounters,
}

/// Main dashboard application state (legacy structure for compatibility)
#[derive(Debug)]
struct SystemDashboard {
    // Application state
    should_quit: bool,
    current_view: DashboardView,
    selected_panel: usize,
    is_paused: bool,

    // Real-time data
    last_update: Instant,
    update_interval: Duration,

    // System metrics with historical data
    cpu_usage: MetricHistory,
    memory_usage: MetricHistory,
    network_upload: MetricHistory,
    network_download: MetricHistory,
    disk_usage: MetricHistory,

    // Process information
    processes: Vec<ProcessInfo>,

    // System information
    system_info: SystemInfo,

    // Network statistics
    network_stats: NetworkStats,

    // Performance counters
    performance_counters: PerformanceCounters,
}

/// Different dashboard views available
#[derive(Debug, Clone, Copy, PartialEq)]
enum DashboardView {
    Overview,
    Performance,
    Processes,
    Network,
    Storage,
}

/// Historical metric data with configurable retention
#[derive(Debug, Clone)]
struct MetricHistory {
    values: VecDeque<f64>,
    max_size: usize,
    current: f64,
    min: f64,
    max: f64,
    average: f64,
}

/// Process information structure
#[derive(Debug, Clone)]
struct ProcessInfo {
    name: String,
    pid: u32,
    cpu_percent: f64,
    memory_mb: f64,
    status: ProcessStatus,
}

/// Process status enumeration
#[derive(Debug, Clone)]
enum ProcessStatus {
    Running,
    Sleeping,
    Stopped,
    Zombie,
}

/// System information structure
#[derive(Debug, Clone)]
struct SystemInfo {
    os_name: String,
    kernel_version: String,
    architecture: String,
    hostname: String,
    uptime: Duration,
    boot_time: Instant,
    cpu_cores: u32,
    total_memory_gb: f64,
}

/// Network statistics
#[derive(Debug, Clone)]
struct NetworkStats {
    active_connections: u32,
    packets_sent: u64,
    packets_received: u64,
    bytes_sent: u64,
    bytes_received: u64,
    interface_count: u32,
}

/// Performance counters for detailed metrics
#[derive(Debug, Clone)]
struct PerformanceCounters {
    context_switches: u64,
    interrupts: u64,
    system_calls: u64,
    page_faults: u64,
    cache_hits: u64,
    cache_misses: u64,
}

impl AppState {
    /// Create a new application state with initialized data for use with hooks
    fn new() -> Self {
        let now = Instant::now();

        Self {
            should_quit: false,
            current_view: DashboardView::Overview,
            selected_panel: 0,
            is_paused: false,
            last_update: now,
            update_interval: Duration::from_millis(500),

            cpu_usage: MetricHistory::new(100),
            memory_usage: MetricHistory::new(100),
            network_upload: MetricHistory::new(100),
            network_download: MetricHistory::new(100),
            disk_usage: MetricHistory::new(100),

            processes: Self::generate_sample_processes(),
            system_info: SystemInfo::new(),
            network_stats: NetworkStats::new(),
            performance_counters: PerformanceCounters::new(),
        }
    }

    /// Generate sample process data for demonstration
    fn generate_sample_processes() -> Vec<ProcessInfo> {
        vec![
            ProcessInfo {
                name: "chrome.exe".to_string(),
                pid: 1234,
                cpu_percent: 15.2,
                memory_mb: 512.8,
                status: ProcessStatus::Running,
            },
            ProcessInfo {
                name: "node.exe".to_string(),
                pid: 5678,
                cpu_percent: 8.7,
                memory_mb: 256.4,
                status: ProcessStatus::Running,
            },
            ProcessInfo {
                name: "rust-analyzer".to_string(),
                pid: 9012,
                cpu_percent: 5.1,
                memory_mb: 128.2,
                status: ProcessStatus::Running,
            },
            ProcessInfo {
                name: "firefox.exe".to_string(),
                pid: 3456,
                cpu_percent: 4.3,
                memory_mb: 384.6,
                status: ProcessStatus::Running,
            },
            ProcessInfo {
                name: "code.exe".to_string(),
                pid: 7890,
                cpu_percent: 3.8,
                memory_mb: 192.3,
                status: ProcessStatus::Running,
            },
        ]
    }
}

impl SystemDashboard {
    /// Create a new system dashboard with initialized data
    fn new() -> Self {
        let now = Instant::now();

        Self {
            should_quit: false,
            current_view: DashboardView::Overview,
            selected_panel: 0,
            is_paused: false,
            last_update: now,
            update_interval: Duration::from_millis(500),

            cpu_usage: MetricHistory::new(100),
            memory_usage: MetricHistory::new(100),
            network_upload: MetricHistory::new(100),
            network_download: MetricHistory::new(100),
            disk_usage: MetricHistory::new(100),

            processes: Self::generate_sample_processes(),
            system_info: SystemInfo::new(),
            network_stats: NetworkStats::new(),
            performance_counters: PerformanceCounters::new(),
        }
    }

    /// Generate sample process data for demonstration
    fn generate_sample_processes() -> Vec<ProcessInfo> {
        vec![
            ProcessInfo {
                name: "chrome.exe".to_string(),
                pid: 1234,
                cpu_percent: 15.2,
                memory_mb: 512.8,
                status: ProcessStatus::Running,
            },
            ProcessInfo {
                name: "node.exe".to_string(),
                pid: 5678,
                cpu_percent: 8.7,
                memory_mb: 256.4,
                status: ProcessStatus::Running,
            },
            ProcessInfo {
                name: "rust-analyzer".to_string(),
                pid: 9012,
                cpu_percent: 5.1,
                memory_mb: 128.2,
                status: ProcessStatus::Running,
            },
            ProcessInfo {
                name: "firefox.exe".to_string(),
                pid: 3456,
                cpu_percent: 4.3,
                memory_mb: 384.6,
                status: ProcessStatus::Running,
            },
            ProcessInfo {
                name: "code.exe".to_string(),
                pid: 7890,
                cpu_percent: 3.8,
                memory_mb: 192.3,
                status: ProcessStatus::Running,
            },
        ]
    }

    /// Update system metrics with simulated real-time data
    fn update_metrics(&mut self) {
        if self.is_paused {
            return;
        }

        let now = Instant::now();
        if now.duration_since(self.last_update) >= self.update_interval {
            // Simulate CPU usage fluctuation
            let cpu_delta = (fastrand::f64() - 0.5) * 10.0;
            let new_cpu = (self.cpu_usage.current + cpu_delta).clamp(0.0, 100.0);
            self.cpu_usage.add_value(new_cpu);

            // Simulate memory usage with gradual changes
            let mem_delta = (fastrand::f64() - 0.5) * 2.0;
            let new_mem = (self.memory_usage.current + mem_delta).clamp(10.0, 90.0);
            self.memory_usage.add_value(new_mem);

            // Simulate network activity
            let upload_base = 1.2 + (fastrand::f64() - 0.5) * 0.8;
            let download_base = 5.8 + (fastrand::f64() - 0.5) * 3.0;
            self.network_upload.add_value(upload_base.max(0.0));
            self.network_download.add_value(download_base.max(0.0));

            // Simulate disk usage (slower changes)
            if fastrand::f64() < 0.1 {
                let disk_delta = (fastrand::f64() - 0.5) * 0.5;
                let new_disk = (self.disk_usage.current + disk_delta).clamp(20.0, 85.0);
                self.disk_usage.add_value(new_disk);
            }

            // Update process CPU usage
            for process in &mut self.processes {
                let delta = (fastrand::f64() - 0.5) * 2.0;
                process.cpu_percent = (process.cpu_percent + delta).clamp(0.0, 25.0);
            }

            // Update network statistics
            self.network_stats.packets_sent += fastrand::u64(10..100);
            self.network_stats.packets_received += fastrand::u64(50..500);
            self.network_stats.active_connections = 40 + fastrand::u32(0..20);

            // Update performance counters
            self.performance_counters.context_switches += fastrand::u64(100..1000);
            self.performance_counters.interrupts += fastrand::u64(50..500);
            self.performance_counters.system_calls += fastrand::u64(200..2000);

            self.last_update = now;
        }
    }

    /// Handle keyboard input for navigation and control
    fn handle_input(&mut self, key: KeyCode) {
        match key {
            KeyCode::Char('q') | KeyCode::Char('Q') => {
                self.should_quit = true;
            }
            KeyCode::Char(' ') => {
                self.is_paused = !self.is_paused;
            }
            KeyCode::Char('r') | KeyCode::Char('R') => {
                self.reset_metrics();
            }
            KeyCode::Char('1') => self.current_view = DashboardView::Overview,
            KeyCode::Char('2') => self.current_view = DashboardView::Performance,
            KeyCode::Char('3') => self.current_view = DashboardView::Processes,
            KeyCode::Char('4') => self.current_view = DashboardView::Network,
            KeyCode::Char('5') => self.current_view = DashboardView::Storage,
            KeyCode::Tab => {
                self.selected_panel = (self.selected_panel + 1) % 4;
            }
            KeyCode::BackTab => {
                self.selected_panel = if self.selected_panel == 0 {
                    3
                } else {
                    self.selected_panel - 1
                };
            }
            _ => {}
        }
    }

    /// Reset all metrics to initial values
    fn reset_metrics(&mut self) {
        self.cpu_usage.reset();
        self.memory_usage.reset();
        self.network_upload.reset();
        self.network_download.reset();
        self.disk_usage.reset();
        self.performance_counters = PerformanceCounters::new();
    }

    /// Render the main dashboard interface using RSX
    fn render(&self) -> VNode {
        match self.current_view {
            DashboardView::Overview => self.render_overview(),
            DashboardView::Performance => self.render_performance(),
            DashboardView::Processes => self.render_processes(),
            DashboardView::Network => self.render_network(),
            DashboardView::Storage => self.render_storage(),
        }
    }

    /// Render the overview dashboard with all main metrics
    fn render_overview(&self) -> VNode {
        let status_text = format!(
            "System Online | CPU: {:.1}% | Memory: {:.1}GB/{:.1}GB | Uptime: {}",
            self.cpu_usage.current,
            self.memory_usage.current * self.system_info.total_memory_gb / 100.0,
            self.system_info.total_memory_gb,
            self.format_uptime()
        );

        let processes_text = self
            .processes
            .iter()
            .take(5)
            .map(|p| format!("• {} - {:.1}% CPU", p.name, p.cpu_percent))
            .collect::<Vec<_>>()
            .join("\n");

        let network_text = format!(
            "↑ Upload: {:.1} MB/s\n↓ Download: {:.1} MB/s\n📡 Connections: {} active\n🌐 Packets: {} sent, {} received",
            self.network_upload.current,
            self.network_download.current,
            self.network_stats.active_connections,
            self.network_stats.packets_sent,
            self.network_stats.packets_received
        );

        let footer_text = format!(
            "OS: {} | Kernel: {} | Arch: {} | [1-5] Views | [Space] Pause | [R] Reset | [Q] Quit {}",
            self.system_info.os_name,
            self.system_info.kernel_version,
            self.system_info.architecture,
            if self.is_paused {
                "⏸ PAUSED"
            } else {
                "▶ LIVE"
            }
        );

        rsx! {
            <Layout
                direction={Direction::Vertical}
                constraints={vec![
                    Constraint::Min(0),
                    Constraint::Length(3)
                ]}
            >
                <Block
                    title={"🖥️  Professional System Dashboard - Overview"}
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Cyan).add_modifier(Modifier::BOLD)}
                >
                    <Layout
                        direction={Direction::Vertical}
                        constraints={vec![
                            Constraint::Length(3),
                            Constraint::Min(0)
                        ]}
                    >
                        <Block
                            title={"📊 System Status"}
                            borders={Borders::ALL}
                            border_style={if self.selected_panel == 0 {
                                Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)
                            } else {
                                Style::default().fg(Color::Green)
                            }}
                        >
                            <Paragraph
                                style={Style::default().fg(Color::White)}
                                alignment={Alignment::Center}
                            >
                                {status_text}
                            </Paragraph>
                        </Block>

                        <Layout
                            direction={Direction::Horizontal}
                            constraints={vec![
                                Constraint::Percentage(50),
                                Constraint::Percentage(50)
                            ]}
                        >
                            <Layout
                                direction={Direction::Vertical}
                                constraints={vec![
                                    Constraint::Percentage(50),
                                    Constraint::Percentage(50)
                                ]}
                            >
                                <Block
                                    title={format!("⚡ CPU Usage - {:.1}% (Avg: {:.1}%)", self.cpu_usage.current, self.cpu_usage.average)}
                                    borders={Borders::ALL}
                                    border_style={if self.selected_panel == 1 {
                                        Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)
                                    } else {
                                        Style::default().fg(Color::Red)
                                    }}
                                >
                                    <Gauge
                                        ratio={self.cpu_usage.current / 100.0}
                                        label={"CPU Usage"}
                                        gauge_style={Style::default().fg(Color::Red)}
                                        use_unicode={true}
                                    />
                                </Block>

                                <Block
                                    title={format!("💾 Memory Usage - {:.1}% (Avg: {:.1}%)", self.memory_usage.current, self.memory_usage.average)}
                                    borders={Borders::ALL}
                                    border_style={if self.selected_panel == 2 {
                                        Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)
                                    } else {
                                        Style::default().fg(Color::Blue)
                                    }}
                                >
                                    <Gauge
                                        ratio={self.memory_usage.current / 100.0}
                                        label={"Memory Usage"}
                                        gauge_style={Style::default().fg(Color::Blue)}
                                        use_unicode={true}
                                    />
                                </Block>
                            </Layout>

                            <Layout
                                direction={Direction::Vertical}
                                constraints={vec![
                                    Constraint::Percentage(50),
                                    Constraint::Percentage(50)
                                ]}
                            >
                                <Block
                                    title={"🔄 Top Processes"}
                                    borders={Borders::ALL}
                                    border_style={if self.selected_panel == 3 {
                                        Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)
                                    } else {
                                        Style::default().fg(Color::Magenta)
                                    }}
                                >
                                    <Paragraph style={Style::default().fg(Color::Magenta)}>
                                        {processes_text}
                                    </Paragraph>
                                </Block>

                                <Block
                                    title={"🌐 Network Activity"}
                                    borders={Borders::ALL}
                                    border_style={Style::default().fg(Color::Cyan)}
                                >
                                    <Paragraph style={Style::default().fg(Color::Cyan)}>
                                        {network_text}
                                    </Paragraph>
                                </Block>
                            </Layout>
                        </Layout>
                    </Layout>
                </Block>
                <Block
                    title={"ℹ️  System Information & Controls"}
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Gray)}
                >
                    <Paragraph
                        style={Style::default().fg(Color::White)}
                        alignment={Alignment::Center}
                    >
                        {footer_text}
                    </Paragraph>
                </Block>
            </Layout>
        }
    }

    /// Render the performance view with detailed metrics
    fn render_performance(&self) -> VNode {
        let perf_text = format!(
            "Context Switches: {}\nInterrupts: {}\nSystem Calls: {}\nPage Faults: {}",
            self.performance_counters.context_switches,
            self.performance_counters.interrupts,
            self.performance_counters.system_calls,
            self.performance_counters.page_faults
        );

        rsx! {
            <Layout
                direction={Direction::Vertical}
                constraints={vec![Constraint::Min(0)]}
            >
                <Block
                    title={"⚡ Performance Metrics"}
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)}
                >
                    <Paragraph style={Style::default().fg(Color::Yellow)}>
                        {perf_text}
                    </Paragraph>
                </Block>
            </Layout>
        }
    }

    /// Render the processes view with detailed process information
    fn render_processes(&self) -> VNode {
        let processes_text = self
            .processes
            .iter()
            .map(|p| {
                format!(
                    "PID: {} | {} | CPU: {:.1}% | Memory: {:.1}MB | Status: {:?}",
                    p.pid, p.name, p.cpu_percent, p.memory_mb, p.status
                )
            })
            .collect::<Vec<_>>()
            .join("\n");

        rsx! {
            <Layout
                direction={Direction::Vertical}
                constraints={vec![Constraint::Min(0)]}
            >
                <Block
                    title={"🔄 Process Manager"}
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Magenta).add_modifier(Modifier::BOLD)}
                >
                    <Paragraph style={Style::default().fg(Color::Magenta)}>
                        {processes_text}
                    </Paragraph>
                </Block>
            </Layout>
        }
    }

    /// Render the network view with detailed network statistics
    fn render_network(&self) -> VNode {
        let network_details = format!(
            "Active Connections: {}\nPackets Sent: {}\nPackets Received: {}\nBytes Sent: {}\nBytes Received: {}\nInterfaces: {}",
            self.network_stats.active_connections,
            self.network_stats.packets_sent,
            self.network_stats.packets_received,
            self.network_stats.bytes_sent,
            self.network_stats.bytes_received,
            self.network_stats.interface_count
        );

        rsx! {
            <Layout
                direction={Direction::Vertical}
                constraints={vec![Constraint::Min(0)]}
            >
                <Block
                    title={"🌐 Network Statistics"}
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Cyan).add_modifier(Modifier::BOLD)}
                >
                    <Paragraph style={Style::default().fg(Color::Cyan)}>
                        {network_details}
                    </Paragraph>
                </Block>
            </Layout>
        }
    }

    /// Render the storage view with disk usage information
    fn render_storage(&self) -> VNode {
        let disk_bar = self.create_progress_bar(self.disk_usage.current, 50);
        let storage_text = format!(
            "Disk Usage: {:.1}%\n{}\n\nTotal Space: 1TB\nFree Space: {:.1}GB\nUsed Space: {:.1}GB",
            self.disk_usage.current,
            disk_bar,
            (100.0 - self.disk_usage.current) * 10.0,
            self.disk_usage.current * 10.0
        );

        rsx! {
            <Layout
                direction={Direction::Vertical}
                constraints={vec![Constraint::Min(0)]}
            >
                <Block
                    title={"💽 Storage Information"}
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Green).add_modifier(Modifier::BOLD)}
                >
                    <Paragraph style={Style::default().fg(Color::Green)}>
                        {storage_text}
                    </Paragraph>
                </Block>
            </Layout>
        }
    }

    /// Create a visual progress bar for metrics
    fn create_progress_bar(&self, percentage: f64, width: usize) -> String {
        let filled = ((percentage / 100.0) * width as f64) as usize;
        let empty = width - filled;
        format!("{}{}", "█".repeat(filled), "░".repeat(empty))
    }

    /// Format uptime duration in a human-readable format
    fn format_uptime(&self) -> String {
        let uptime = self.system_info.uptime;
        let days = uptime.as_secs() / 86400;
        let hours = (uptime.as_secs() % 86400) / 3600;
        let minutes = (uptime.as_secs() % 3600) / 60;

        if days > 0 {
            format!("{}d {}h {}m", days, hours, minutes)
        } else if hours > 0 {
            format!("{}h {}m", hours, minutes)
        } else {
            format!("{}m", minutes)
        }
    }
}

impl MetricHistory {
    /// Create a new metric history with specified retention size
    fn new(max_size: usize) -> Self {
        Self {
            values: VecDeque::new(),
            max_size,
            current: 45.0, // Default starting value
            min: 45.0,
            max: 45.0,
            average: 45.0,
        }
    }

    /// Add a new value to the history and update statistics
    fn add_value(&mut self, value: f64) {
        self.values.push_back(value);
        if self.values.len() > self.max_size {
            self.values.pop_front();
        }

        self.current = value;
        self.min = self.values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        self.max = self.values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        self.average = self.values.iter().sum::<f64>() / self.values.len() as f64;
    }

    /// Reset the metric history to initial state
    fn reset(&mut self) {
        self.values.clear();
        self.current = 45.0;
        self.min = 45.0;
        self.max = 45.0;
        self.average = 45.0;
    }
}

impl SystemInfo {
    /// Create new system information with sample data
    fn new() -> Self {
        let now = Instant::now();
        Self {
            os_name: "Windows 11 Pro".to_string(),
            kernel_version: "10.0.22631".to_string(),
            architecture: "x64".to_string(),
            hostname: "WORKSTATION-01".to_string(),
            uptime: Duration::from_secs(432000), // 5 days
            boot_time: now,                      // Use current time as boot time for demo
            cpu_cores: 8,
            total_memory_gb: 16.0,
        }
    }
}

impl NetworkStats {
    /// Create new network statistics with initial values
    fn new() -> Self {
        Self {
            active_connections: 47,
            packets_sent: 1247,
            packets_received: 3891,
            bytes_sent: 1024 * 1024 * 150,     // 150 MB
            bytes_received: 1024 * 1024 * 890, // 890 MB
            interface_count: 3,
        }
    }
}

impl PerformanceCounters {
    /// Create new performance counters with initial values
    fn new() -> Self {
        Self {
            context_switches: 125000,
            interrupts: 89000,
            system_calls: 456000,
            page_faults: 12000,
            cache_hits: 890000,
            cache_misses: 45000,
        }
    }
}

/// Hook-based application loop with event handling and rendering
///
/// This function demonstrates the professional integration of the event system
/// with the hook-based component architecture. It shows how to:
/// - Set up event context for components to access via use_event hook
/// - Handle events through the hook system rather than manual event handling
/// - Maintain proper hook lifecycle management across render cycles
/// - Integrate event-driven state updates with use_state hooks
fn run_hook_dashboard<B: ratatui::backend::Backend>(
    terminal: &mut Terminal<B>,
    hook_context: Rc<HookContext>,
) -> io::Result<()> {
    println!("🎯 Hook-based dashboard started!");
    println!("📋 Controls: [1-5] Views | [Tab] Navigate | [Space] Pause | [R] Reset | [Q] Quit");

    let mut should_quit = false;

    loop {
        // Handle input events and set them in the event context
        // This allows components to access events via the use_event hook
        if event::poll(Duration::from_millis(100))? {
            let evt = event::read()?;
            // Set the current event in the event context for components to access
            set_current_event_context(Some(Arc::new(evt.clone())));
            // Check for quit condition at the application level
            if let Event::Key(key) = &evt {
                if key.kind == KeyEventKind::Press
                    && matches!(key.code, KeyCode::Char('q') | KeyCode::Char('Q'))
                {
                    should_quit = true;
                }
            }
        };

        // Reset hooks for new render cycle
        hook_context.reset();

        // Render the hook-based dashboard component
        // The component will use use_event() to access the current event
        // and use_state() to manage its reactive state
        terminal.draw(|f| {
            let vnode = dashboard_component();
            let size = f.area();
            f.render_widget(vnode, size);
        })?;

        // Clear the event context after rendering
        set_current_event_context(None);

        // Check if we should quit
        if should_quit {
            break;
        }
    }
    Ok(())
}

/// Main application loop with event handling and rendering (traditional)
fn run_dashboard<B: ratatui::backend::Backend>(
    terminal: &mut Terminal<B>,
    mut app: SystemDashboard,
) -> io::Result<()> {
    loop {
        // Update metrics with real-time simulation
        app.update_metrics();

        // Render the current view
        terminal.draw(|f| {
            let vnode = app.render();
            let size = f.area();
            f.render_widget(vnode, size);
        })?;

        // Handle input events with timeout for real-time updates
        if event::poll(Duration::from_millis(100))? {
            if let Event::Key(key) = event::read()? {
                if key.kind == KeyEventKind::Press {
                    app.handle_input(key.code);
                }
            }
        }

        // Check if application should quit
        if app.should_quit {
            break;
        }
    }
    Ok(())
}

/// Render the overview dashboard with state-driven UI using RSX
fn render_overview_with_state(
    state: &AppState,
    status_text: &str,
    processes_text: &str,
    network_text: &str,
    footer_text: &str,
    selected_panel: usize,
) -> VNode {
    rsx! {
        <Layout
            direction={Direction::Vertical}
            constraints={vec![
                Constraint::Min(0),
                Constraint::Length(3)
            ]}
        >
            <Block
                title={"🖥️  Professional System Dashboard - Overview (with use_state)"}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Cyan).add_modifier(Modifier::BOLD)}
            >
                <Layout
                    direction={Direction::Vertical}
                    constraints={vec![
                        Constraint::Length(3),
                        Constraint::Min(0)
                    ]}
                >
                    <Block
                        title={"📊 System Status"}
                        borders={Borders::ALL}
                        border_style={if selected_panel == 0 {
                            Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)
                        } else {
                            Style::default().fg(Color::Green)
                        }}
                    >
                        <Paragraph
                            style={Style::default().fg(Color::White)}
                            alignment={Alignment::Center}
                        >
                            {status_text.to_string()}
                        </Paragraph>
                    </Block>

                    <Layout
                        direction={Direction::Horizontal}
                        constraints={vec![
                            Constraint::Percentage(50),
                            Constraint::Percentage(50)
                        ]}
                    >
                        <Layout
                            direction={Direction::Vertical}
                            constraints={vec![
                                Constraint::Percentage(50),
                                Constraint::Percentage(50)
                            ]}
                        >
                            <Block
                                title={format!("⚡ CPU Usage - {:.1}% (Avg: {:.1}%)", state.cpu_usage.current, state.cpu_usage.average)}
                                borders={Borders::ALL}
                                border_style={if selected_panel == 1 {
                                    Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)
                                } else {
                                    Style::default().fg(Color::Red)
                                }}
                            >
                                <Gauge
                                    ratio={state.cpu_usage.current / 100.0}
                                    label={"CPU Usage"}
                                    gauge_style={Style::default().fg(Color::Red)}
                                    use_unicode={true}
                                />
                            </Block>

                            <Block
                                title={format!("💾 Memory Usage - {:.1}% (Avg: {:.1}%)", state.memory_usage.current, state.memory_usage.average)}
                                borders={Borders::ALL}
                                border_style={if selected_panel == 2 {
                                    Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)
                                } else {
                                    Style::default().fg(Color::Blue)
                                }}
                            >
                                <Gauge
                                    ratio={state.memory_usage.current / 100.0}
                                    label={"Memory Usage"}
                                    gauge_style={Style::default().fg(Color::Blue)}
                                    use_unicode={true}
                                />
                            </Block>
                        </Layout>

                        <Layout
                            direction={Direction::Vertical}
                            constraints={vec![
                                Constraint::Percentage(50),
                                Constraint::Percentage(50)
                            ]}
                        >
                            <Block
                                title={"🔄 Top Processes"}
                                borders={Borders::ALL}
                                border_style={if selected_panel == 3 {
                                    Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)
                                } else {
                                    Style::default().fg(Color::Magenta)
                                }}
                            >
                                <Paragraph style={Style::default().fg(Color::Magenta)}>
                                    {processes_text.to_string()}
                                </Paragraph>
                            </Block>

                            <Block
                                title={"🌐 Network Activity"}
                                borders={Borders::ALL}
                                border_style={Style::default().fg(Color::Cyan)}
                            >
                                <Paragraph style={Style::default().fg(Color::Cyan)}>
                                    {network_text.to_string()}
                                </Paragraph>
                            </Block>
                        </Layout>
                    </Layout>
                </Layout>
            </Block>
            <Block
                title={"ℹ️  System Information & Controls"}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Gray)}
            >
                <Paragraph
                    style={Style::default().fg(Color::White)}
                    alignment={Alignment::Center}
                >
                    {footer_text.to_string()}
                </Paragraph>
            </Block>
        </Layout>
    }
}

/// Render the performance view with state-driven UI
fn render_performance_with_state(state: &AppState) -> VNode {
    let perf_text = format!(
        "Context Switches: {}\nInterrupts: {}\nSystem Calls: {}\nPage Faults: {}",
        state.performance_counters.context_switches,
        state.performance_counters.interrupts,
        state.performance_counters.system_calls,
        state.performance_counters.page_faults
    );

    rsx! {
        <Layout
            direction={Direction::Vertical}
            constraints={vec![Constraint::Min(0)]}
        >
            <Block
                title={"⚡ Performance Metrics (with use_state)"}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)}
            >
                <Paragraph style={Style::default().fg(Color::Yellow)}>
                    {perf_text}
                </Paragraph>
            </Block>
        </Layout>
    }
}

/// Render the processes view with state-driven UI
fn render_processes_with_state(state: &AppState) -> VNode {
    let processes_text = state
        .processes
        .iter()
        .map(|p| {
            format!(
                "PID: {} | {} | CPU: {:.1}% | Memory: {:.1}MB | Status: {:?}",
                p.pid, p.name, p.cpu_percent, p.memory_mb, p.status
            )
        })
        .collect::<Vec<_>>()
        .join("\n");

    rsx! {
        <Layout
            direction={Direction::Vertical}
            constraints={vec![Constraint::Min(0)]}
        >
            <Block
                title={"🔄 Process Manager (with use_state)"}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Magenta).add_modifier(Modifier::BOLD)}
            >
                <Paragraph style={Style::default().fg(Color::Magenta)}>
                    {processes_text}
                </Paragraph>
            </Block>
        </Layout>
    }
}

/// Render the network view with state-driven UI
fn render_network_with_state(state: &AppState) -> VNode {
    let network_details = format!(
        "Active Connections: {}\nPackets Sent: {}\nPackets Received: {}\nBytes Sent: {}\nBytes Received: {}\nInterfaces: {}",
        state.network_stats.active_connections,
        state.network_stats.packets_sent,
        state.network_stats.packets_received,
        state.network_stats.bytes_sent,
        state.network_stats.bytes_received,
        state.network_stats.interface_count
    );

    rsx! {
        <Layout
            direction={Direction::Vertical}
            constraints={vec![Constraint::Min(0)]}
        >
            <Block
                title={"🌐 Network Statistics (with use_state)"}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Cyan).add_modifier(Modifier::BOLD)}
            >
                <Paragraph style={Style::default().fg(Color::Cyan)}>
                    {network_details}
                </Paragraph>
            </Block>
        </Layout>
    }
}

/// Render the storage view with state-driven UI
fn render_storage_with_state(state: &AppState) -> VNode {
    let disk_bar = create_progress_bar(state.disk_usage.current, 50);
    let storage_text = format!(
        "Disk Usage: {:.1}%\n{}\n\nTotal Space: 1TB\nFree Space: {:.1}GB\nUsed Space: {:.1}GB",
        state.disk_usage.current,
        disk_bar,
        (100.0 - state.disk_usage.current) * 10.0,
        state.disk_usage.current * 10.0
    );

    rsx! {
        <Layout
            direction={Direction::Vertical}
            constraints={vec![Constraint::Min(0)]}
        >
            <Block
                title={"💽 Storage Information (with use_state)"}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Green).add_modifier(Modifier::BOLD)}
            >
                <Paragraph style={Style::default().fg(Color::Green)}>
                    {storage_text}
                </Paragraph>
            </Block>
        </Layout>
    }
}

/// Create a visual progress bar for metrics
fn create_progress_bar(percentage: f64, width: usize) -> String {
    let filled = ((percentage / 100.0) * width as f64) as usize;
    let empty = width - filled;
    format!("{}{}", "█".repeat(filled), "░".repeat(empty))
}

/// Professional Counter Component demonstrating use_state setter functionality
///
/// This component showcases the complete use_state workflow including:
/// - State initialization with default values
/// - State reading for UI rendering
/// - State updates through setter functions
/// - Functional updates for safe state modifications
///
/// ## Example Usage:
/// ```rust
/// let counter_vnode = counter_component();
/// ```
///
/// ## State Management Pattern:
/// 1. Initialize: `let (count, set_count) = use_state(0);`
/// 2. Read: `let current_value = count.get();`
/// 3. Update: `set_count.set(new_value)` or `set_count.update(|prev| prev + 1)`
#[allow(dead_code)]
fn counter_component() -> VNode {
    // Initialize counter state using use_state hook
    let (count_state, _set_count) = use_state(0i32);

    // Get current count value for rendering
    let current_count = count_state.get();

    // Create display text with current count
    let counter_text = format!(
        "Professional Counter Example\n\nCurrent Count: {}\n\nThis demonstrates:\n• use_state hook initialization\n• State reading with .get()\n• State updates with setter functions\n• Professional component architecture",
        current_count
    );

    // Note: In a full implementation, we would handle button clicks
    // to demonstrate state updates like:
    //
    // on_increment_click: move || set_count.update(|prev| prev + 1)
    // on_decrement_click: move || set_count.update(|prev| prev - 1)
    // on_reset_click: move || set_count.set(0)

    rsx! {
        <Layout
            direction={Direction::Vertical}
            constraints={vec![Constraint::Min(0)]}
        >
            <Block
                title={"🔢 Professional Counter Component (use_state Demo)"}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Green).add_modifier(Modifier::BOLD)}
            >
                <Paragraph
                    style={Style::default().fg(Color::Green)}
                    alignment={Alignment::Center}
                >
                    {counter_text}
                </Paragraph>
            </Block>
        </Layout>
    }
}

/// Professional State Update Examples Component
///
/// This component demonstrates various patterns for updating state
/// using the use_state hook in professional applications.
#[allow(dead_code)]
fn state_update_examples_component() -> VNode {
    // Example 1: Simple primitive state
    let (counter, _set_counter) = use_state(42i32);

    // Example 2: Complex struct state
    #[derive(Clone, Debug)]
    struct UserProfile {
        name: String,
        age: u32,
        active: bool,
    }

    let (profile, _set_profile) = use_state(UserProfile {
        name: "John Doe".to_string(),
        age: 30,
        active: true,
    });

    // Example 3: Collection state
    let (items, _set_items) = use_state(vec!["Item 1".to_string(), "Item 2".to_string()]);

    // Get current values for display
    let current_counter = counter.get();
    let current_profile = profile.get();
    let current_items = items.get();

    let examples_text = format!(
        "State Update Patterns:\n\n1. Primitive State:\n   Counter: {}\n   • set_counter.set(100)\n   • set_counter.update(|prev| prev + 1)\n\n2. Struct State:\n   Profile: {} (age: {}, active: {})\n   • set_profile.update(|prev| UserProfile {{ age: prev.age + 1, ..prev }})\n\n3. Collection State:\n   Items: {} items\n   • set_items.update(|prev| {{ let mut new_items = prev; new_items.push(\"New Item\".to_string()); new_items }})",
        current_counter,
        current_profile.name,
        current_profile.age,
        current_profile.active,
        current_items.len()
    );

    rsx! {
        <Layout
            direction={Direction::Vertical}
            constraints={vec![Constraint::Min(0)]}
        >
            <Block
                title={"📚 Professional State Update Patterns (use_state)"}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Blue).add_modifier(Modifier::BOLD)}
            >
                <Paragraph
                    style={Style::default().fg(Color::Blue)}
                >
                    {examples_text}
                </Paragraph>
            </Block>
        </Layout>
    }
}

/// Advanced Multi-Hook Integration Component
///
/// This component demonstrates professional patterns for using multiple hooks
/// together in complex scenarios, showcasing the power of the hook system.
#[allow(dead_code)]
fn multi_hook_integration_component() -> VNode {
    // Multiple state hooks working together
    let (app_config, _set_app_config) = use_state(AppConfig::default());
    let (user_preferences, _set_user_preferences) = use_state(UserPreferences::default());
    let (session_data, _set_session_data) = use_state(SessionData::default());

    // Derived state using field access for performance
    let theme = app_config.field(|config| config.theme.clone());
    let language = user_preferences.field(|prefs| prefs.language.clone());
    let session_id = session_data.field(|session| session.id.clone());

    // Complex state computation
    let status_text = format!(
        "🔧 Multi-Hook Integration Demo\n\nApp Configuration:\n• Theme: {}\n• Debug Mode: {}\n\nUser Preferences:\n• Language: {}\n• Notifications: {}\n\nSession Data:\n• Session ID: {}\n• Connected: {}\n• Uptime: {}s",
        theme,
        app_config.field(|c| c.debug_mode),
        language,
        user_preferences.field(|p| p.notifications_enabled),
        session_id,
        session_data.field(|s| s.connected),
        session_data.field(|s| s.uptime_seconds)
    );

    rsx! {
        <Layout
            direction={Direction::Vertical}
            constraints={vec![Constraint::Min(0)]}
        >
            <Block
                title={"🔗 Advanced Multi-Hook Integration (use_state)"}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Magenta).add_modifier(Modifier::BOLD)}
            >
                <Paragraph
                    style={Style::default().fg(Color::Magenta)}
                >
                    {status_text}
                </Paragraph>
            </Block>
        </Layout>
    }
}

/// Application configuration structure for multi-hook demo
#[derive(Clone, Debug)]
struct AppConfig {
    theme: String,
    debug_mode: bool,
    max_connections: u32,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            theme: "Dark".to_string(),
            debug_mode: false,
            max_connections: 100,
        }
    }
}

/// User preferences structure for multi-hook demo
#[derive(Clone, Debug)]
struct UserPreferences {
    language: String,
    notifications_enabled: bool,
    auto_save: bool,
}

impl Default for UserPreferences {
    fn default() -> Self {
        Self {
            language: "English".to_string(),
            notifications_enabled: true,
            auto_save: true,
        }
    }
}

/// Session data structure for multi-hook demo
#[derive(Clone, Debug)]
struct SessionData {
    id: String,
    connected: bool,
    uptime_seconds: u64,
}

impl Default for SessionData {
    fn default() -> Self {
        Self {
            id: "sess_12345".to_string(),
            connected: true,
            uptime_seconds: 3600,
        }
    }
}
