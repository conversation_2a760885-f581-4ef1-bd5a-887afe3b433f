/*!
# Professional System Dashboard with use_state Hook Integration

This comprehensive example demonstrates the professional integration of the `use_state` hook
from the rink-leptos framework into a complex TUI application. It showcases both traditional
state management and modern hook-based reactive state management patterns.

## Key Features Implemented:

### 🎯 Hook-Based State Management
- **React-like `use_state` hook**: Complete integration with `use_state(initial_value)`
- **Professional component architecture**: Functional components with hook-based state
- **Thread-safe state access**: Arc<StateContainer<T>> with RwLock for concurrent access
- **Efficient field access**: `state.field(|s| s.field_name)` for minimal cloning
- **Hook context management**: Proper lifecycle management with HookContext

### 🏗️ Architecture Patterns
- **Dual implementation approach**: Both hook-based and traditional implementations
- **Component separation**: Clear separation between UI logic and state management
- **Professional error handling**: Type-safe state access with clear error messages
- **Memory efficiency**: Arc-based sharing without unnecessary deep cloning

### 📊 Complex State Structures
- **Nested state management**: Complex `AppState` with multiple sub-structures
- **Real-time data simulation**: System metrics with historical tracking
- **Multi-view dashboard**: Different views with state-driven UI rendering
- **Professional UI components**: RSX-based components with state integration

### 🔧 Professional Development Patterns
- **Comprehensive documentation**: Detailed examples and usage patterns
- **Type safety**: Full Rust type system integration with hooks
- **Performance optimization**: Efficient rendering and state updates
- **Scalable architecture**: Supports complex component hierarchies

## Usage:

Run the application and choose between:
1. **Hook-based implementation** (Recommended) - Uses `use_state` for reactive state management
2. **Traditional implementation** (Legacy) - Uses manual state management

## Components Demonstrated:

- `dashboard_component()` - Main dashboard using `use_state`
- `counter_component()` - Simple counter demonstrating hook patterns
- `state_update_examples_component()` - Various state update patterns

## State Management Patterns:

```rust
// Initialize state
let (app_state, set_app_state) = use_state(AppState::new());

// Read state
let current_state = app_state.get();
let cpu_usage = app_state.field(|s| s.cpu_usage.current);

// Update state (ready for implementation)
set_app_state.set(new_state);
set_app_state.update(|prev| updated_state);
```

This example serves as a comprehensive reference for integrating `use_state` hooks
into professional TUI applications with the rink-leptos framework.
*/

use crossterm::{
    event::{self, DisableMouseCapture, EnableMouseCapture, Event, KeyCode, KeyEventKind},
    execute,
    terminal::{EnterAlternateScreen, LeaveAlternateScreen, disable_raw_mode, enable_raw_mode},
};
use ratatui::{
    Terminal,
    backend::CrosstermBackend,
    layout::{Alignment, Constraint, Direction},
    style::{Color, Modifier, Style},
    widgets::Borders,
};
use rink::prelude::*;
use rink_core::hooks::effect::use_effect;
use rink_core::hooks::event::{set_current_event_context, use_event};
use rink_core::hooks::interval::use_interval;
use rink_core::hooks::reducer::use_reducer;
use rink_core::hooks::state::use_state;
use rink_core::hooks::{HookContext, clear_hook_context, set_hook_context};
use std::{io, rc::Rc, sync::Arc, time::Duration};

/// Professional System Monitoring Dashboard with Comprehensive Hook Integration
///
/// This comprehensive example demonstrates the full capabilities of the rink-leptos
/// RSX framework for building complex terminal user interfaces with modern hook-based
/// state management and side effects. Features include:
///
/// - **use_reducer hook**: Professional Redux-like state management with predictable updates
/// - **use_effect hook**: Side effects, lifecycle management, and dependency tracking
/// - **use_interval hook**: Periodic execution with automatic cleanup and pause/resume
/// - **use_event hook**: Professional event handling and user interaction
/// - **Gauge widgets**: Advanced progress visualization with RSX integration
/// - Multi-panel dashboard layout with responsive design
/// - Interactive navigation between different views
/// - Live data visualization with historical tracking
/// - Professional styling and user experience
/// - Comprehensive error handling and state management
/// - Modern React/Redux-like component architecture with hooks
/// - Type-safe action dispatching with compile-time guarantees
/// - Automatic cleanup and resource management
///
/// ## Hook Integration Patterns Demonstrated:
///
/// ### 1. use_reducer for State Management
/// - Centralized state with predictable action-based updates
/// - Immutable state transitions with clear action types
/// - Type-safe dispatching with compile-time guarantees
///
/// ### 2. use_effect for Side Effects
/// - Automatic data simulation and real-time updates
/// - Dependency tracking with tuple-based dependencies
/// - Proper cleanup functions for resource management
/// - View-specific effects that respond to state changes
///
/// ### 3. use_event for User Interaction
/// - Professional keyboard and mouse event handling
/// - Integration with reducer actions for state updates
/// - Real-time responsiveness and user feedback
///
/// Navigation:
/// - Tab/Shift+Tab: Navigate between panels
/// - 1-5: Switch between different dashboard views
/// - Space: Pause/Resume real-time updates
/// - R: Reset all metrics
/// - U: Simulate data update (demonstrates action dispatching)
/// - Q: Quit application
fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 Professional System Dashboard with Comprehensive Hook Integration");
    println!("🎯 Modern React-like TUI with use_reducer, use_effect, and use_event hooks");
    println!("🔧 Features: State Management + Side Effects + Event Handling + Gauge Widgets");
    println!(
        "📋 Controls: [1-5] Views | [Tab] Navigate | [Space] Pause | [R] Reset | [U] Update | [Q] Quit"
    );

    run_hook_based_dashboard()
}

/// Run the comprehensive hook-based dashboard implementation
///
/// This function demonstrates the integration of multiple hooks:
/// - use_reducer: For predictable state management
/// - use_effect: For side effects and lifecycle management
/// - use_event: For professional event handling
/// - Gauge widgets: For advanced data visualization
fn run_hook_based_dashboard() -> Result<(), Box<dyn std::error::Error>> {
    println!("🎯 Starting Comprehensive Hook-Based Dashboard...");

    // Setup terminal with proper error handling
    enable_raw_mode()?;
    let mut stdout = io::stdout();
    execute!(stdout, EnterAlternateScreen, EnableMouseCapture)?;
    let backend = CrosstermBackend::new(stdout);
    let mut terminal = Terminal::new(backend)?;

    // Create hook context for the component system
    let hook_context = Rc::new(HookContext::new());
    set_hook_context(hook_context.clone());

    // Run the hook-based dashboard with proper error handling
    let res = run_hook_dashboard(&mut terminal, hook_context);

    // Cleanup and restore terminal state
    clear_hook_context();
    disable_raw_mode()?;
    execute!(
        terminal.backend_mut(),
        LeaveAlternateScreen,
        DisableMouseCapture
    )?;
    terminal.show_cursor()?;

    if let Err(err) = res {
        eprintln!("Hook-based dashboard error: {:?}", err);
    }

    Ok(())
}

/// Professional Event Handler for Reducer-Based Dashboard Component
///
/// This function demonstrates professional event handling with use_reducer,
/// showing how to dispatch actions instead of directly updating state.
///
/// ## Key Features:
/// - Dispatches typed actions for predictable state updates
/// - Maintains separation between event handling and state logic
/// - Type-safe action creation with compile-time guarantees
/// - Professional error handling and validation
/// - Demonstrates Redux-like action dispatching patterns
fn handle_dashboard_event_with_reducer(
    event: &crossterm::event::Event,
    dispatch: &rink_core::hooks::reducer::DispatchFn<DashboardAction>,
) {
    match event {
        crossterm::event::Event::Key(key_event) => {
            // Only handle key press events, ignore key release
            if key_event.kind != crossterm::event::KeyEventKind::Press {
                return;
            }

            match key_event.code {
                // Quit application
                crossterm::event::KeyCode::Char('q') | crossterm::event::KeyCode::Char('Q') => {
                    dispatch.call(DashboardAction::Quit);
                }

                // Toggle pause/resume
                crossterm::event::KeyCode::Char(' ') => {
                    dispatch.call(DashboardAction::TogglePause);
                }

                // Reset metrics
                crossterm::event::KeyCode::Char('r') | crossterm::event::KeyCode::Char('R') => {
                    dispatch.call(DashboardAction::Reset);
                }

                // View switching with number keys
                crossterm::event::KeyCode::Char('1') => {
                    dispatch.call(DashboardAction::SwitchView(DashboardView::Overview));
                }
                crossterm::event::KeyCode::Char('2') => {
                    dispatch.call(DashboardAction::SwitchView(DashboardView::Performance));
                }
                crossterm::event::KeyCode::Char('3') => {
                    dispatch.call(DashboardAction::SwitchView(DashboardView::Processes));
                }
                crossterm::event::KeyCode::Char('4') => {
                    dispatch.call(DashboardAction::SwitchView(DashboardView::Network));
                }
                crossterm::event::KeyCode::Char('5') => {
                    dispatch.call(DashboardAction::SwitchView(DashboardView::Storage));
                }

                // Panel navigation with Tab
                crossterm::event::KeyCode::Tab => {
                    dispatch.call(DashboardAction::NextPanel);
                }

                // Reverse panel navigation with Shift+Tab
                crossterm::event::KeyCode::BackTab => {
                    dispatch.call(DashboardAction::PreviousPanel);
                }

                // Simulate data update for demonstration
                crossterm::event::KeyCode::Char('u') | crossterm::event::KeyCode::Char('U') => {
                    dispatch.call(DashboardAction::SimulateDataUpdate);
                }

                // Handle other key events (can be extended)
                _ => {
                    // For demonstration, we could handle more complex key combinations here
                }
            }
        }

        // Handle mouse events (future enhancement)
        crossterm::event::Event::Mouse(_mouse_event) => {
            // Mouse event handling can be implemented here
        }

        // Handle resize events
        crossterm::event::Event::Resize(_width, _height) => {
            // Terminal resize handling can be implemented here
        }

        // Handle other event types
        _ => {
            // Other events can be handled here
        }
    }
}

/// Professional Dashboard Component using use_state hook
///
/// This component demonstrates the professional integration of the use_state hook
/// from the rink-leptos framework for reactive state management in TUI applications.
///
/// ## Key Features Demonstrated:
///
/// ### 1. React-like State Management
/// - Uses `use_state(AppState::new())` for centralized state management
/// - Provides automatic re-rendering when state changes (future implementation)
/// - Thread-safe state access with Arc<StateContainer<T>> under the hood
///
/// ### 2. Complex State Structures
/// - Manages nested state with `AppState` containing multiple sub-structures
/// - Efficient field access using `app_state.field(|s| s.field_name)`
/// - Clones only necessary data, not the entire state structure
///
/// ### 3. Professional State Access Patterns
/// ```rust
/// let (app_state, set_app_state) = use_state(AppState::new());
/// let current_state = app_state.get();  // Get full state snapshot
/// let cpu_usage = app_state.field(|s| s.cpu_usage.current);  // Efficient field access
/// ```
///
/// ### 4. Hook Context Integration
/// - Properly integrates with HookContext for component lifecycle management
/// - Supports multiple hooks per component with automatic indexing
/// - Thread-local storage for hook state persistence across renders
///
/// ### 5. Functional State Updates (Ready for Implementation)
/// ```rust
/// // Direct value updates
/// set_app_state.set(new_state);
///
/// // Functional updates for safe concurrent access
/// set_app_state.update(|prev_state| {
///     AppState {
///         cpu_usage: updated_cpu_usage,
///         ..prev_state
///     }
/// });
/// ```
///
/// ### 6. Professional Error Handling
/// - Panics with clear error messages when hooks are called outside component context
/// - Type-safe state access with compile-time guarantees
/// - Memory-safe concurrent access using RwLock and Mutex
///
/// ### 7. Performance Optimizations
/// - Uses Arc for efficient state sharing without deep cloning
/// - RwLock enables concurrent reads while maintaining write safety
/// - Version tracking for efficient change detection (future optimization)
///
/// ## Architecture Benefits:
///
/// - **Separation of Concerns**: UI logic separated from state management
/// - **Testability**: State can be easily mocked and tested independently
/// - **Maintainability**: Clear state flow and predictable updates
/// - **Scalability**: Hook system supports complex component hierarchies
/// - **Performance**: Minimal re-renders and efficient memory usage
///
/// ## Usage Pattern:
///
/// This component follows the React functional component pattern:
/// 1. Initialize state with `use_state(initial_value)`
/// 2. Extract values for rendering using `.get()` or `.field()`
/// 3. Render UI based on current state
/// 4. Update state through setter functions (event handlers)
/// 5. Framework handles re-rendering automatically
///
/// ## Integration with RSX:
///
/// The component seamlessly integrates with the RSX macro system,
/// providing a familiar React-like development experience while
/// leveraging Rust's type safety and performance characteristics.
/// Professional Dashboard Component using use_reducer and use_effect hooks
///
/// This component demonstrates the professional integration of multiple hooks
/// for complex state management, side effects, and lifecycle management.
///
/// ## Key Features Demonstrated:
///
/// ### 1. Redux-like State Management with use_reducer
/// - Uses `use_reducer(dashboard_reducer, AppState::new())` for centralized state management
/// - Predictable state updates through action dispatching
/// - Immutable state transitions with clear action types
///
/// ### 2. Professional Side Effects with use_effect
/// - Automatic data simulation and updates using effect hooks
/// - Cleanup functions for proper resource management
/// - Dependency tracking for optimized effect execution
///
/// ### 3. Periodic Execution with use_interval
/// - Real-time data updates every 2 seconds using interval hooks
/// - Automatic pause/resume based on application state
/// - Proper cleanup when intervals are no longer needed
///
/// ### 3. Professional Action Dispatching
/// ```rust
/// let (state, dispatch) = use_reducer(dashboard_reducer, AppState::new());
/// dispatch.call(DashboardAction::SwitchView(DashboardView::Performance));
/// ```
///
/// ### 4. Complex State Management Patterns
/// - Handles multiple related state updates atomically
/// - Type-safe action dispatching with compile-time guarantees
/// - Clear separation between state logic and UI logic
///
/// ### 5. Event Integration
/// - Seamlessly integrates with use_event hook for professional event handling
/// - Actions are dispatched in response to user interactions
/// - Maintains single source of truth for all state updates
fn dashboard_component() -> VNode {
    // Initialize application state using the use_reducer hook
    // This provides Redux-like state management with predictable state transitions
    let (app_state, dispatch) = use_reducer(dashboard_reducer, AppState::new());

    // Professional side effect for automatic data simulation
    // This demonstrates use_effect integration with use_reducer for real-time updates
    let dispatch_clone = dispatch.clone();
    use_effect(
        move || {
            // Simulate real-time data updates every render cycle
            // In a real application, this would be triggered by timers or external events
            dispatch_clone.call(DashboardAction::SimulateDataUpdate);

            // Return cleanup function for proper resource management
            Some(Box::new(move || {
                // Cleanup logic would go here (e.g., clearing timers, closing connections)
                // For demonstration, we just log the cleanup
                #[cfg(debug_assertions)]
                eprintln!("Dashboard effect cleanup executed");
            }) as Box<dyn FnOnce() + Send>)
        },
        (),
    ); // Empty dependencies = run once per component lifecycle

    // Advanced effect with dependency tracking using tuples
    // This demonstrates how effects can respond to specific state changes
    let current_view = app_state.field(|s| s.current_view);
    let selected_panel = app_state.field(|s| s.selected_panel);
    let is_paused = app_state.field(|s| s.is_paused);
    let dispatch_clone2 = dispatch.clone();

    // Convert enum to discriminant for dependency tracking
    let view_discriminant = current_view as u8;

    use_effect(
        move || {
            // Effect runs when the current view, selected panel, or pause state changes
            // This demonstrates tuple-based dependency tracking
            #[cfg(debug_assertions)]
            eprintln!(
                "Dashboard state changed - View: {:?}, Panel: {}, Paused: {}",
                current_view, selected_panel, is_paused
            );

            // Simulate state-specific side effects
            if !is_paused {
                match current_view {
                    DashboardView::Performance => {
                        // Could trigger performance metrics collection
                        dispatch_clone2.call(DashboardAction::SimulateDataUpdate);
                    }
                    DashboardView::Network => {
                        // Could trigger network stats refresh
                        dispatch_clone2.call(DashboardAction::SimulateDataUpdate);
                    }
                    _ => {
                        // Default behavior for other views
                    }
                }
            }

            // Return cleanup function for state-specific cleanup
            Some(Box::new(move || {
                #[cfg(debug_assertions)]
                eprintln!(
                    "State effect cleanup - View: {:?}, Panel: {}, Paused: {}",
                    current_view, selected_panel, is_paused
                );
            }) as Box<dyn FnOnce() + Send>)
        },
        (view_discriminant, selected_panel, is_paused), // Tuple dependencies - runs when any change
    );

    // Professional interval hook for real-time data updates
    // This demonstrates use_interval integration with use_reducer for periodic updates
    let dispatch_clone3 = dispatch.clone();
    let is_paused_for_interval = app_state.field(|s| s.is_paused);

    // Only run interval when not paused
    if !is_paused_for_interval {
        use_interval(
            move || {
                // Periodic data simulation every 2 seconds
                dispatch_clone3.call(DashboardAction::SimulateDataUpdate);

                #[cfg(debug_assertions)]
                eprintln!("Interval-based data update triggered");
            },
            Duration::from_secs(2), // Update every 2 seconds
        );
    }

    // Use the event hook to handle terminal events professionally
    // This demonstrates the integration of use_event with use_reducer
    if let Some(event) = use_event() {
        handle_dashboard_event_with_reducer(&event, &dispatch);
    }

    // Extract current values from state for rendering
    let current_state = app_state.get();
    let _should_quit = current_state.should_quit;
    let current_view = current_state.current_view;
    let selected_panel = current_state.selected_panel;
    let is_paused = current_state.is_paused;

    // Create individual state handles for different metrics using field access
    let cpu_usage = app_state.field(|s| s.cpu_usage.current);
    let memory_usage = app_state.field(|s| s.memory_usage.current);
    let network_upload = app_state.field(|s| s.network_upload.current);
    let network_download = app_state.field(|s| s.network_download.current);

    // Professional status text generation using state values
    let status_text = format!(
        "System Online | CPU: {:.1}% | Memory: {:.1}GB/{:.1}GB | Uptime: {} | Status: {} | State: use_reducer",
        cpu_usage,
        memory_usage * current_state.system_info.total_memory_gb / 100.0,
        current_state.system_info.total_memory_gb,
        format_uptime_duration(current_state.system_info.uptime),
        if is_paused { "⏸ PAUSED" } else { "▶ LIVE" }
    );

    // Generate process information text from state
    let processes_text = current_state
        .processes
        .iter()
        .take(5)
        .map(|p| format!("• {} - {:.1}% CPU", p.name, p.cpu_percent))
        .collect::<Vec<_>>()
        .join("\n");

    // Network activity information from state
    let network_text = format!(
        "↑ Upload: {:.1} MB/s\n↓ Download: {:.1} MB/s\n📡 Connections: {} active\n🌐 Packets: {} sent, {} received",
        network_upload,
        network_download,
        current_state.network_stats.active_connections,
        current_state.network_stats.packets_sent,
        current_state.network_stats.packets_received
    );

    // Footer with system information and controls
    let footer_text = format!(
        "OS: {} | Kernel: {} | Arch: {} | [1-5] Views | [Tab] Navigate | [Space] Pause | [R] Reset | [Q] Quit {}",
        current_state.system_info.os_name,
        current_state.system_info.kernel_version,
        current_state.system_info.architecture,
        if is_paused { "⏸ PAUSED" } else { "▶ LIVE" }
    );

    // Render the dashboard using RSX with state-driven UI
    match current_view {
        DashboardView::Overview => render_overview_with_state(
            &current_state,
            &status_text,
            &processes_text,
            &network_text,
            &footer_text,
            selected_panel,
        ),
        DashboardView::Performance => render_performance_with_state(&current_state),
        DashboardView::Processes => render_processes_with_state(&current_state),
        DashboardView::Network => render_network_with_state(&current_state),
        DashboardView::Storage => render_storage_with_state(&current_state),
    }
}

/// Helper function to format uptime duration
fn format_uptime_duration(uptime: Duration) -> String {
    let days = uptime.as_secs() / 86400;
    let hours = (uptime.as_secs() % 86400) / 3600;
    let minutes = (uptime.as_secs() % 3600) / 60;

    if days > 0 {
        format!("{}d {}h {}m", days, hours, minutes)
    } else if hours > 0 {
        format!("{}h {}m", hours, minutes)
    } else {
        format!("{}m", minutes)
    }
}

/// Application state structure for use with hooks
///
/// This structure demonstrates professional state management patterns
/// with the use_state hook, including complex nested data structures
/// and efficient field access patterns.
#[derive(Debug, Clone)]
struct AppState {
    // Application state
    should_quit: bool,
    current_view: DashboardView,
    selected_panel: usize,
    is_paused: bool,

    // System metrics with historical data
    cpu_usage: MetricHistory,
    memory_usage: MetricHistory,
    network_upload: MetricHistory,
    network_download: MetricHistory,
    disk_usage: MetricHistory,

    // Process information
    processes: Vec<ProcessInfo>,

    // System information
    system_info: SystemInfo,

    // Network statistics
    network_stats: NetworkStats,

    // Performance counters
    performance_counters: PerformanceCounters,
}

/// Different dashboard views available
#[derive(Debug, Clone, Copy, PartialEq)]
enum DashboardView {
    Overview,
    Performance,
    Processes,
    Network,
    Storage,
}

/// Professional Dashboard Actions for use_reducer
///
/// This enum defines all possible actions that can be dispatched to update
/// the dashboard state. It demonstrates professional action design patterns
/// for complex state management with the use_reducer hook.
#[derive(Debug, Clone)]
enum DashboardAction {
    // Navigation actions
    SwitchView(DashboardView),
    NavigatePanel(usize),
    NextPanel,
    PreviousPanel,

    // Control actions
    TogglePause,
    Quit,
    Reset,

    // Data update actions
    UpdateMetrics {
        cpu: f64,
        memory: f64,
        network_up: f64,
        network_down: f64,
        disk: f64,
    },
    UpdateProcesses(Vec<ProcessInfo>),
    UpdateNetworkStats(NetworkStats),
    UpdatePerformanceCounters(PerformanceCounters),

    // System actions
    RefreshSystemInfo,
    SimulateDataUpdate,
}

/// Historical metric data with configurable retention
#[derive(Debug, Clone)]
struct MetricHistory {
    current: f64,
    average: f64,
}

/// Process information structure
#[derive(Debug, Clone)]
struct ProcessInfo {
    name: String,
    pid: u32,
    cpu_percent: f64,
    memory_mb: f64,
    status: ProcessStatus,
}

/// Process status enumeration
#[derive(Debug, Clone)]
enum ProcessStatus {
    Running,
}

/// System information structure
#[derive(Debug, Clone)]
struct SystemInfo {
    os_name: String,
    kernel_version: String,
    architecture: String,
    uptime: Duration,
    total_memory_gb: f64,
}

/// Network statistics
#[derive(Debug, Clone)]
struct NetworkStats {
    active_connections: u32,
    packets_sent: u64,
    packets_received: u64,
    bytes_sent: u64,
    bytes_received: u64,
    interface_count: u32,
}

/// Performance counters for detailed metrics
#[derive(Debug, Clone)]
struct PerformanceCounters {
    context_switches: u64,
    interrupts: u64,
    system_calls: u64,
    page_faults: u64,
}

/// Professional Dashboard Reducer Function
///
/// This function demonstrates professional reducer patterns for complex state management.
/// It handles all dashboard actions in a predictable, immutable way, making state updates
/// easy to reason about and debug.
///
/// ## Key Features:
/// - Immutable state updates following Redux patterns
/// - Comprehensive action handling with clear state transitions
/// - Professional error handling and validation
/// - Optimized for performance with minimal cloning
/// - Type-safe action dispatching with compile-time guarantees
fn dashboard_reducer(state: AppState, action: DashboardAction) -> AppState {
    match action {
        // Navigation actions
        DashboardAction::SwitchView(view) => AppState {
            current_view: view,
            ..state
        },

        DashboardAction::NavigatePanel(panel) => AppState {
            selected_panel: panel.min(4), // Clamp to valid range (0-4 for 5 panels)
            ..state
        },

        DashboardAction::NextPanel => AppState {
            selected_panel: (state.selected_panel + 1) % 5, // Cycle through 5 panels (0-4)
            ..state
        },

        DashboardAction::PreviousPanel => AppState {
            selected_panel: if state.selected_panel == 0 {
                4 // Go to last panel (panel 4)
            } else {
                state.selected_panel - 1
            },
            ..state
        },

        // Control actions
        DashboardAction::TogglePause => AppState {
            is_paused: !state.is_paused,
            ..state
        },

        DashboardAction::Quit => AppState {
            should_quit: true,
            ..state
        },

        DashboardAction::Reset => AppState {
            cpu_usage: MetricHistory::new(),
            memory_usage: MetricHistory::new(),
            network_upload: MetricHistory::new(),
            network_download: MetricHistory::new(),
            disk_usage: MetricHistory::new(),
            performance_counters: PerformanceCounters::new(),
            ..state
        },

        // Data update actions
        DashboardAction::UpdateMetrics {
            cpu,
            memory,
            network_up,
            network_down,
            disk,
        } => {
            let mut new_state = state;
            new_state.cpu_usage.add_value(cpu);
            new_state.memory_usage.add_value(memory);
            new_state.network_upload.add_value(network_up);
            new_state.network_download.add_value(network_down);
            new_state.disk_usage.add_value(disk);
            new_state
        }

        DashboardAction::UpdateProcesses(processes) => AppState { processes, ..state },

        DashboardAction::UpdateNetworkStats(network_stats) => AppState {
            network_stats,
            ..state
        },

        DashboardAction::UpdatePerformanceCounters(performance_counters) => AppState {
            performance_counters,
            ..state
        },

        // System actions
        DashboardAction::RefreshSystemInfo => {
            // In a real implementation, this would refresh system information
            state
        }

        DashboardAction::SimulateDataUpdate => {
            // Simulate real-time data updates
            let cpu_delta = (fastrand::f64() - 0.5) * 10.0;
            let new_cpu = (state.cpu_usage.current + cpu_delta).clamp(0.0, 100.0);

            let mem_delta = (fastrand::f64() - 0.5) * 2.0;
            let new_mem = (state.memory_usage.current + mem_delta).clamp(10.0, 90.0);

            let upload_base = 1.2 + (fastrand::f64() - 0.5) * 0.8;
            let download_base = 5.8 + (fastrand::f64() - 0.5) * 3.0;
            let current_disk = state.disk_usage.current; // Store before move

            dashboard_reducer(
                state,
                DashboardAction::UpdateMetrics {
                    cpu: new_cpu,
                    memory: new_mem,
                    network_up: upload_base.max(0.0),
                    network_down: download_base.max(0.0),
                    disk: current_disk, // Disk changes slowly
                },
            )
        }
    }
}

impl AppState {
    /// Create a new application state with initialized data for use with hooks
    fn new() -> Self {
        Self {
            should_quit: false,
            current_view: DashboardView::Overview,
            selected_panel: 0,
            is_paused: false,

            cpu_usage: MetricHistory::new(),
            memory_usage: MetricHistory::new(),
            network_upload: MetricHistory::new(),
            network_download: MetricHistory::new(),
            disk_usage: MetricHistory::new(),

            processes: Self::generate_sample_processes(),
            system_info: SystemInfo::new(),
            network_stats: NetworkStats::new(),
            performance_counters: PerformanceCounters::new(),
        }
    }

    /// Generate sample process data for demonstration
    fn generate_sample_processes() -> Vec<ProcessInfo> {
        vec![
            ProcessInfo {
                name: "chrome.exe".to_string(),
                pid: 1234,
                cpu_percent: 15.2,
                memory_mb: 512.8,
                status: ProcessStatus::Running,
            },
            ProcessInfo {
                name: "node.exe".to_string(),
                pid: 5678,
                cpu_percent: 8.7,
                memory_mb: 256.4,
                status: ProcessStatus::Running,
            },
            ProcessInfo {
                name: "rust-analyzer".to_string(),
                pid: 9012,
                cpu_percent: 5.1,
                memory_mb: 128.2,
                status: ProcessStatus::Running,
            },
            ProcessInfo {
                name: "firefox.exe".to_string(),
                pid: 3456,
                cpu_percent: 4.3,
                memory_mb: 384.6,
                status: ProcessStatus::Running,
            },
            ProcessInfo {
                name: "code.exe".to_string(),
                pid: 7890,
                cpu_percent: 3.8,
                memory_mb: 192.3,
                status: ProcessStatus::Running,
            },
        ]
    }
}

impl MetricHistory {
    /// Create a new metric history with specified retention size
    fn new() -> Self {
        Self {
            current: 45.0, // Default starting value
            average: 45.0,
        }
    }

    /// Add a new value to the metric history
    fn add_value(&mut self, value: f64) {
        self.current = value;
        // Simple moving average calculation
        self.average = (self.average * 0.9) + (value * 0.1);
    }
}

impl SystemInfo {
    /// Create new system information with sample data
    fn new() -> Self {
        Self {
            os_name: "Windows 11 Pro".to_string(),
            kernel_version: "10.0.22631".to_string(),
            architecture: "x64".to_string(),
            uptime: Duration::from_secs(432000), // 5 days
            total_memory_gb: 16.0,
        }
    }
}

impl NetworkStats {
    /// Create new network statistics with initial values
    fn new() -> Self {
        Self {
            active_connections: 47,
            packets_sent: 1247,
            packets_received: 3891,
            bytes_sent: 1024 * 1024 * 150,     // 150 MB
            bytes_received: 1024 * 1024 * 890, // 890 MB
            interface_count: 3,
        }
    }
}

impl PerformanceCounters {
    /// Create new performance counters with initial values
    fn new() -> Self {
        Self {
            context_switches: 125000,
            interrupts: 89000,
            system_calls: 456000,
            page_faults: 12000,
        }
    }
}

/// Hook-based application loop with event handling and rendering
///
/// This function demonstrates the professional integration of the event system
/// with the hook-based component architecture. It shows how to:
/// - Set up event context for components to access via use_event hook
/// - Handle events through the hook system rather than manual event handling
/// - Maintain proper hook lifecycle management across render cycles
/// - Integrate event-driven state updates with use_state hooks
fn run_hook_dashboard<B: ratatui::backend::Backend>(
    terminal: &mut Terminal<B>,
    hook_context: Rc<HookContext>,
) -> io::Result<()> {
    println!("🎯 Hook-based dashboard started!");
    println!("📋 Controls: [1-5] Views | [Tab] Navigate | [Space] Pause | [R] Reset | [Q] Quit");

    let mut should_quit = false;

    loop {
        // Handle input events and set them in the event context
        // This allows components to access events via the use_event hook
        if event::poll(Duration::from_millis(16))? {
            let evt = event::read()?;
            // Set the current event in the event context for components to access
            set_current_event_context(Some(Arc::new(evt.clone())));
            // Check for quit condition at the application level
            if let Event::Key(key) = &evt {
                if key.kind == KeyEventKind::Press
                    && matches!(key.code, KeyCode::Char('q') | KeyCode::Char('Q'))
                {
                    should_quit = true;
                }
            }
        };

        // Reset hooks for new render cycle
        hook_context.reset();

        // Render the hook-based dashboard component
        // The component demonstrates use_reducer hook integration for professional state management
        // Event handling is shown with action dispatching patterns
        terminal.draw(|f| {
            let vnode = dashboard_component();
            let size = f.area();
            f.render_widget(vnode, size);
        })?;

        // Clear the event context after rendering
        set_current_event_context(None);

        // Check if we should quit
        if should_quit {
            break;
        }
    }
    Ok(())
}

/// Render the overview dashboard with state-driven UI using RSX
fn render_overview_with_state(
    state: &AppState,
    status_text: &str,
    processes_text: &str,
    network_text: &str,
    footer_text: &str,
    selected_panel: usize,
) -> VNode {
    rsx! {
        <Layout
            direction={Direction::Vertical}
            constraints={vec![
                Constraint::Min(0),
                Constraint::Length(3)
            ]}
        >
            <Block
                title={"🖥️  Professional System Dashboard - Overview (with use_state)"}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Cyan).add_modifier(Modifier::BOLD)}
            >
                <Layout
                    direction={Direction::Vertical}
                    constraints={vec![
                        Constraint::Length(3),
                        Constraint::Min(0)
                    ]}
                >
                    <Block
                        title={"📊 System Status"}
                        borders={Borders::ALL}
                        border_style={if selected_panel == 0 {
                            Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)
                        } else {
                            Style::default().fg(Color::Green)
                        }}
                    >
                        <Paragraph
                            style={Style::default().fg(Color::White)}
                            alignment={Alignment::Center}
                        >
                            {status_text.to_string()}
                        </Paragraph>
                    </Block>

                    <Layout
                        direction={Direction::Horizontal}
                        constraints={vec![
                            Constraint::Percentage(50),
                            Constraint::Percentage(50)
                        ]}
                    >
                        <Layout
                            direction={Direction::Vertical}
                            constraints={vec![
                                Constraint::Percentage(50),
                                Constraint::Percentage(50)
                            ]}
                        >
                            <Block
                                title={format!("⚡ CPU Usage - {:.1}% (Avg: {:.1}%)", state.cpu_usage.current, state.cpu_usage.average)}
                                borders={Borders::ALL}
                                border_style={if selected_panel == 1 {
                                    Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)
                                } else {
                                    Style::default().fg(Color::Red)
                                }}
                            >
                                <Gauge
                                    ratio={state.cpu_usage.current / 100.0}
                                    label={"CPU Usage"}
                                    gauge_style={Style::default().fg(Color::Red)}
                                    use_unicode={true}
                                />
                            </Block>

                            <Block
                                title={format!("💾 Memory Usage - {:.1}% (Avg: {:.1}%)", state.memory_usage.current, state.memory_usage.average)}
                                borders={Borders::ALL}
                                border_style={if selected_panel == 2 {
                                    Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)
                                } else {
                                    Style::default().fg(Color::Blue)
                                }}
                            >
                                <Gauge
                                    ratio={state.memory_usage.current / 100.0}
                                    label={"Memory Usage"}
                                    gauge_style={Style::default().fg(Color::Blue)}
                                    use_unicode={true}
                                />
                            </Block>
                        </Layout>

                        <Layout
                            direction={Direction::Vertical}
                            constraints={vec![
                                Constraint::Percentage(50),
                                Constraint::Percentage(50)
                            ]}
                        >
                            <Block
                                title={"🔄 Top Processes"}
                                borders={Borders::ALL}
                                border_style={if selected_panel == 3 {
                                    Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)
                                } else {
                                    Style::default().fg(Color::Magenta)
                                }}
                            >
                                <Paragraph style={Style::default().fg(Color::Magenta)}>
                                    {processes_text.to_string()}
                                </Paragraph>
                            </Block>

                            <Block
                                title={"🌐 Network Activity"}
                                borders={Borders::ALL}
                                border_style={if selected_panel == 4 {
                                    Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)
                                } else {
                                    Style::default().fg(Color::Cyan)
                                }}
                            >
                                <Paragraph style={Style::default().fg(Color::Cyan)}>
                                    {network_text.to_string()}
                                </Paragraph>
                            </Block>
                        </Layout>
                    </Layout>
                </Layout>
            </Block>
            <Block
                title={"ℹ️  System Information & Controls"}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Gray)}
            >
                <Paragraph
                    style={Style::default().fg(Color::White)}
                    alignment={Alignment::Center}
                >
                    {footer_text.to_string()}
                </Paragraph>
            </Block>
        </Layout>
    }
}

/// Render the performance view with state-driven UI and comprehensive Gauge examples
fn render_performance_with_state(state: &AppState) -> VNode {
    rsx! {
        <Layout
            direction={Direction::Vertical}
            constraints={vec![
                Constraint::Length(3),
                Constraint::Min(0)
            ]}
        >
            <Block
                title={"⚡ Performance Metrics Dashboard (Professional Gauge Examples)"}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD)}
            >
            </Block>

            <Layout
                direction={Direction::Horizontal}
                constraints={vec![
                    Constraint::Percentage(50),
                    Constraint::Percentage(50)
                ]}
            >
                <Layout
                    direction={Direction::Vertical}
                    constraints={vec![
                        Constraint::Percentage(25),
                        Constraint::Percentage(25),
                        Constraint::Percentage(25),
                        Constraint::Percentage(25)
                    ]}
                >
                    <Block
                        title={"🔥 CPU Load (Ratio-based)"}
                        borders={Borders::ALL}
                        border_style={Style::default().fg(Color::Red)}
                    >
                        <Gauge
                            ratio={state.cpu_usage.current / 100.0}
                            gauge_style={Style::default().fg(Color::Red)}
                            use_unicode={true}
                        />
                    </Block>

                    <Block
                        title={"💾 Memory Usage (Percent-based)"}
                        borders={Borders::ALL}
                        border_style={Style::default().fg(Color::Blue)}
                    >
                        <Gauge
                            percent={state.memory_usage.current as u16}
                            label={"RAM"}
                            gauge_style={Style::default().fg(Color::Blue)}
                            use_unicode={false}
                        />
                    </Block>

                    <Block
                        title={"🌐 Network Upload (Custom Label)"}
                        borders={Borders::ALL}
                        border_style={Style::default().fg(Color::Green)}
                    >
                        <Gauge
                            ratio={(state.network_upload.current / 10.0).min(1.0)}
                            label={format!("{:.1} MB/s", state.network_upload.current)}
                            gauge_style={Style::default().fg(Color::Green)}
                            use_unicode={true}
                        />
                    </Block>

                    <Block
                        title={"📡 Network Download (High Precision)"}
                        borders={Borders::ALL}
                        border_style={Style::default().fg(Color::Cyan)}
                    >
                        <Gauge
                            ratio={(state.network_download.current / 20.0).min(1.0)}
                            label={format!("{:.2} MB/s", state.network_download.current)}
                            gauge_style={Style::default().fg(Color::Cyan)}
                            use_unicode={true}
                        />
                    </Block>
                </Layout>

                <Layout
                    direction={Direction::Vertical}
                    constraints={vec![
                        Constraint::Percentage(25),
                        Constraint::Percentage(25),
                        Constraint::Percentage(25),
                        Constraint::Percentage(25)
                    ]}
                >
                    <Block
                        title={"💽 Disk Usage (No Unicode)"}
                        borders={Borders::ALL}
                        border_style={Style::default().fg(Color::Magenta)}
                    >
                        <Gauge
                            ratio={state.disk_usage.current / 100.0}
                            label={"Storage"}
                            gauge_style={Style::default().fg(Color::Magenta)}
                            use_unicode={false}
                        />
                    </Block>

                    <Block
                        title={"⚙️ Context Switches (Simulated)"}
                        borders={Borders::ALL}
                        border_style={Style::default().fg(Color::Yellow)}
                    >
                        <Gauge
                            ratio={(state.performance_counters.context_switches % 1000) as f64 / 1000.0}
                            label={"Switches/sec"}
                            gauge_style={Style::default().fg(Color::Yellow)}
                            use_unicode={true}
                        />
                    </Block>

                    <Block
                        title={"🔄 System Calls (Normalized)"}
                        borders={Borders::ALL}
                        border_style={Style::default().fg(Color::LightBlue)}
                    >
                        <Gauge
                            ratio={(state.performance_counters.system_calls % 500) as f64 / 500.0}
                            gauge_style={Style::default().fg(Color::LightBlue)}
                            use_unicode={true}
                        />
                    </Block>

                    <Block
                        title={"📄 Page Faults (Percentage)"}
                        borders={Borders::ALL}
                        border_style={Style::default().fg(Color::LightRed)}
                    >
                        <Gauge
                            percent={(state.performance_counters.page_faults % 100) as u16}
                            label={"Faults"}
                            gauge_style={Style::default().fg(Color::LightRed)}
                            use_unicode={false}
                        />
                    </Block>
                </Layout>
            </Layout>
        </Layout>
    }
}

/// Render the processes view with state-driven UI
fn render_processes_with_state(state: &AppState) -> VNode {
    let processes_text = state
        .processes
        .iter()
        .map(|p| {
            format!(
                "PID: {} | {} | CPU: {:.1}% | Memory: {:.1}MB | Status: {:?}",
                p.pid, p.name, p.cpu_percent, p.memory_mb, p.status
            )
        })
        .collect::<Vec<_>>()
        .join("\n");

    rsx! {
        <Layout
            direction={Direction::Vertical}
            constraints={vec![Constraint::Min(0)]}
        >
            <Block
                title={"🔄 Process Manager (with use_state)"}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Magenta).add_modifier(Modifier::BOLD)}
            >
                <Paragraph style={Style::default().fg(Color::Magenta)}>
                    {processes_text}
                </Paragraph>
            </Block>
        </Layout>
    }
}

/// Render the network view with state-driven UI
fn render_network_with_state(state: &AppState) -> VNode {
    let network_details = format!(
        "Active Connections: {}\nPackets Sent: {}\nPackets Received: {}\nBytes Sent: {}\nBytes Received: {}\nInterfaces: {}",
        state.network_stats.active_connections,
        state.network_stats.packets_sent,
        state.network_stats.packets_received,
        state.network_stats.bytes_sent,
        state.network_stats.bytes_received,
        state.network_stats.interface_count
    );

    rsx! {
        <Layout
            direction={Direction::Vertical}
            constraints={vec![Constraint::Min(0)]}
        >
            <Block
                title={"🌐 Network Statistics (with use_state)"}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Cyan).add_modifier(Modifier::BOLD)}
            >
                <Paragraph style={Style::default().fg(Color::Cyan)}>
                    {network_details}
                </Paragraph>
            </Block>
        </Layout>
    }
}

/// Render the storage view with state-driven UI and advanced Gauge examples
fn render_storage_with_state(state: &AppState) -> VNode {
    let total_disk_gb = 1000.0; // 1TB
    let used_gb = state.disk_usage.current * 10.0;
    let free_gb = total_disk_gb - used_gb;

    rsx! {
        <Layout
            direction={Direction::Vertical}
            constraints={vec![
                Constraint::Length(3),
                Constraint::Min(0)
            ]}
        >
            <Block
                title={"💽 Storage Dashboard (Advanced Gauge Demonstrations)"}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Magenta).add_modifier(Modifier::BOLD)}
            >
            </Block>

            <Layout
                direction={Direction::Vertical}
                constraints={vec![
                    Constraint::Percentage(20),
                    Constraint::Percentage(20),
                    Constraint::Percentage(20),
                    Constraint::Percentage(20),
                    Constraint::Percentage(20)
                ]}
            >
                <Block
                    title={format!("🗄️ Primary Disk Usage - {:.1}GB/{:.1}GB", used_gb, total_disk_gb)}
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Red)}
                >
                    <Gauge
                        ratio={state.disk_usage.current / 100.0}
                        label={format!("{:.1}% Full", state.disk_usage.current)}
                        gauge_style={if state.disk_usage.current > 80.0 {
                            Style::default().fg(Color::Red)
                        } else if state.disk_usage.current > 60.0 {
                            Style::default().fg(Color::Yellow)
                        } else {
                            Style::default().fg(Color::Green)
                        }}
                        use_unicode={true}
                    />
                </Block>

                <Block
                    title={"📁 Cache Directory (Simulated)"}
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Blue)}
                >
                    <Gauge
                        percent={((state.cpu_usage.current * 1.3) % 100.0) as u16}
                        label={"Cache"}
                        gauge_style={Style::default().fg(Color::Blue)}
                        use_unicode={true}
                    />
                </Block>

                <Block
                    title={"📄 Temporary Files (Dynamic)"}
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Cyan)}
                >
                    <Gauge
                        ratio={((state.memory_usage.current * 0.7) % 100.0) / 100.0}
                        label={"Temp"}
                        gauge_style={Style::default().fg(Color::Cyan)}
                        use_unicode={false}
                    />
                </Block>

                <Block
                    title={"🗃️ Database Storage (Calculated)"}
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Green)}
                >
                    <Gauge
                        ratio={((state.network_upload.current * 15.0) % 100.0) / 100.0}
                        label={format!("{:.1}GB", (state.network_upload.current * 15.0) % 100.0 * 0.5)}
                        gauge_style={Style::default().fg(Color::Green)}
                        use_unicode={true}
                    />
                </Block>

                <Block
                    title={"🔄 Backup Progress (Animated)"}
                    borders={Borders::ALL}
                    border_style={Style::default().fg(Color::Yellow)}
                >
                    <Gauge
                        ratio={((state.network_download.current * 8.0) % 100.0) / 100.0}
                        label={"Backup"}
                        gauge_style={Style::default().fg(Color::Yellow)}
                        use_unicode={true}
                    />
                </Block>
            </Layout>
        </Layout>
    }
}

/// Professional Counter Component demonstrating use_state setter functionality
///
/// This component showcases the complete use_state workflow including:
/// - State initialization with default values
/// - State reading for UI rendering
/// - State updates through setter functions
/// - Functional updates for safe state modifications
///
/// ## Example Usage:
/// ```rust
/// let counter_vnode = counter_component();
/// ```
///
/// ## State Management Pattern:
/// 1. Initialize: `let (count, set_count) = use_state(0);`
/// 2. Read: `let current_value = count.get();`
/// 3. Update: `set_count.set(new_value)` or `set_count.update(|prev| prev + 1)`
#[allow(dead_code)]
fn counter_component() -> VNode {
    // Initialize counter state using use_state hook
    let (count_state, _set_count) = use_state(0i32);

    // Get current count value for rendering
    let current_count = count_state.get();

    // Create display text with current count
    let counter_text = format!(
        "Professional Counter Example\n\nCurrent Count: {}\n\nThis demonstrates:\n• use_state hook initialization\n• State reading with .get()\n• State updates with setter functions\n• Professional component architecture",
        current_count
    );

    // Note: In a full implementation, we would handle button clicks
    // to demonstrate state updates like:
    //
    // on_increment_click: move || set_count.update(|prev| prev + 1)
    // on_decrement_click: move || set_count.update(|prev| prev - 1)
    // on_reset_click: move || set_count.set(0)

    rsx! {
        <Layout
            direction={Direction::Vertical}
            constraints={vec![Constraint::Min(0)]}
        >
            <Block
                title={"🔢 Professional Counter Component (use_state Demo)"}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Green).add_modifier(Modifier::BOLD)}
            >
                <Paragraph
                    style={Style::default().fg(Color::Green)}
                    alignment={Alignment::Center}
                >
                    {counter_text}
                </Paragraph>
            </Block>
        </Layout>
    }
}

/// Professional State Update Examples Component
///
/// This component demonstrates various patterns for updating state
/// using the use_state hook in professional applications.
#[allow(dead_code)]
fn state_update_examples_component() -> VNode {
    // Example 1: Simple primitive state
    let (counter, _set_counter) = use_state(42i32);

    // Example 2: Complex struct state
    #[derive(Clone, Debug)]
    struct UserProfile {
        name: String,
        age: u32,
        active: bool,
    }

    let (profile, _set_profile) = use_state(UserProfile {
        name: "John Doe".to_string(),
        age: 30,
        active: true,
    });

    // Example 3: Collection state
    let (items, _set_items) = use_state(vec!["Item 1".to_string(), "Item 2".to_string()]);

    // Get current values for display
    let current_counter = counter.get();
    let current_profile = profile.get();
    let current_items = items.get();

    let examples_text = format!(
        "State Update Patterns:\n\n1. Primitive State:\n   Counter: {}\n   • set_counter.set(100)\n   • set_counter.update(|prev| prev + 1)\n\n2. Struct State:\n   Profile: {} (age: {}, active: {})\n   • set_profile.update(|prev| UserProfile {{ age: prev.age + 1, ..prev }})\n\n3. Collection State:\n   Items: {} items\n   • set_items.update(|prev| {{ let mut new_items = prev; new_items.push(\"New Item\".to_string()); new_items }})",
        current_counter,
        current_profile.name,
        current_profile.age,
        current_profile.active,
        current_items.len()
    );

    rsx! {
        <Layout
            direction={Direction::Vertical}
            constraints={vec![Constraint::Min(0)]}
        >
            <Block
                title={"📚 Professional State Update Patterns (use_state)"}
                borders={Borders::ALL}
                border_style={Style::default().fg(Color::Blue).add_modifier(Modifier::BOLD)}
            >
                <Paragraph
                    style={Style::default().fg(Color::Blue)}
                >
                    {examples_text}
                </Paragraph>
            </Block>
        </Layout>
    }
}
