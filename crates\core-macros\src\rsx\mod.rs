use proc_macro::TokenStream;
use quote::quote;
use syn::{
    Expr, Ident, Path, Result, Token,
    parse::{Parse, ParseStream},
    token::Brace,
};

use crate::internals::ErrorContext;
use crate::utils::Symbol;

// Widget type constants for professional symbol handling
const LAYOUT: Symbol = Symbol::new("Layout");
const BLOCK: Symbol = Symbol::new("Block");
const PARAGRAPH: Symbol = Symbol::new("Paragraph");
const LINE: Symbol = Symbol::new("Line");
const GAUGE: Symbol = Symbol::new("Gauge");

// Supported widget types for component detection
const SUPPORTED_WIDGETS: &[Symbol] = &[LAYOUT, BLOCK, PARAGRAPH, LINE, GAUGE];

// Simplified AST structures - only Element and Expression
#[derive(Debug, Clone)]
pub enum Node {
    Element(Element),
    Expression(Expr),
}

#[derive(Debug, Clone)]
pub struct Element {
    pub name: Path,
    pub attributes: Vec<Prop>,
    pub children: Vec<Node>,
}

#[derive(Debug, Clone)]
pub struct Prop {
    pub key: Ident,
    pub value: Expr,
}

// Main implementation of the rsx! macro with professional error handling
pub fn rsx_impl(input: TokenStream) -> TokenStream {
    let error_ctx = ErrorContext::new();

    // Parse the input with error handling
    let node = match syn::parse::<Node>(input) {
        Ok(node) => node,
        Err(err) => {
            error_ctx.add_syntax_error(err);
            return error_ctx
                .check_errors()
                .unwrap_err()
                .to_compile_error()
                .into();
        }
    };

    // Validate the parsed node structure with error context
    validate_node_structure(&node, &error_ctx);

    // Generate the expanded code with error context
    let expanded = generate_node_code_with_context(&node, &error_ctx);

    // Check for any errors that occurred during validation or code generation
    if let Err(combined_error) = error_ctx.check_errors() {
        return combined_error.to_compile_error().into();
    }

    // Return the generated code wrapped in proper scope
    quote! {
        {
            use rink_core::VNode;
            #expanded
        }
    }
    .into()
}

// Professional validation function for node structure with error context
fn validate_node_structure(node: &Node, error_ctx: &ErrorContext) {
    match node {
        Node::Element(element) => {
            validate_element_structure(element, error_ctx);
        }
        Node::Expression(expr) => {
            validate_expression_structure(expr, error_ctx);
        }
    }
}

// Validate element structure and handle edge cases
fn validate_element_structure(element: &Element, error_ctx: &ErrorContext) {
    let name = &element.name;
    let name_str = quote!(#name).to_string();

    // Check for empty element names
    if name_str.is_empty() {
        error_ctx.add_error_with_span(name, "Element name cannot be empty");
        return;
    }

    // Check for invalid characters in element names
    if name_str.contains(char::is_whitespace) {
        error_ctx.add_error_with_span(name, "Element name cannot contain whitespace");
        return;
    }

    // Extract widget type for validation
    let widget_type = name_str.split("::").last().unwrap_or(&name_str);

    // Validate supported widget types
    if !SUPPORTED_WIDGETS.iter().any(|widget| widget == widget_type) {
        let first_char = name_str.chars().next().unwrap_or('_');
        let is_component = first_char.is_uppercase() && !name_str.contains("::");

        if !is_component {
            error_ctx.add_error_with_span(
                name,
                format!(
                    "Unsupported widget type '{}'. Supported widgets: Layout, Block, Paragraph, Line, Gauge",
                    widget_type
                )
            );
        }
    }

    // Validate attributes - basic validation
    for attr in &element.attributes {
        let key = &attr.key;
        let key_str = quote!(#key).to_string();

        // Check for empty attribute keys
        if key_str.is_empty() {
            error_ctx.add_error_with_span(key, "Attribute key cannot be empty");
        }

        // Check for invalid characters in attribute keys
        if key_str.contains(char::is_whitespace) {
            error_ctx.add_error_with_span(key, "Attribute key cannot contain whitespace");
        }
    }

    // Validate children recursively
    for child in &element.children {
        validate_node_structure(child, error_ctx);
    }

    // Validate widget-specific constraints
    validate_widget_constraints(element, error_ctx);
}

// Validate expression structure
fn validate_expression_structure(expr: &Expr, error_ctx: &ErrorContext) {
    // Check for potentially problematic expressions
    match expr {
        Expr::Unsafe(_) => {
            error_ctx.add_error_with_span(expr, "Unsafe expressions are not allowed in RSX");
        }
        Expr::Async(_) => {
            error_ctx.add_error_with_span(expr, "Async expressions are not supported in RSX");
        }
        _ => {
            // Most expressions are valid
        }
    }
}

// Validate widget-specific constraints
fn validate_widget_constraints(element: &Element, error_ctx: &ErrorContext) {
    let name = &element.name;
    let name_str = quote!(#name).to_string();
    let widget_type = name_str.split("::").last().unwrap_or(&name_str);

    match widget_type {
        "Layout" => {
            // Layout should have children
            if element.children.is_empty() {
                error_ctx.add_error_with_span(
                    &element.name,
                    "Layout component should have children to arrange",
                );
            }
        }
        "Line" => {
            // Line should not have element children when used standalone
            if element
                .children
                .iter()
                .any(|child| matches!(child, Node::Element(_)))
            {
                error_ctx.add_error_with_span(
                    &element.name,
                    "Line component should not contain element children when used standalone",
                );
            }
        }
        _ => {
            // Other widgets have no specific constraints
        }
    }
}

// Enhanced code generation with error context
fn generate_node_code_with_context(
    node: &Node,
    error_ctx: &ErrorContext,
) -> proc_macro2::TokenStream {
    match node {
        Node::Element(element) => generate_element_code_with_context(element, error_ctx),
        Node::Expression(expr) => {
            quote! { VNode::text(#expr) }
        }
    }
}

// Enhanced element code generation with error context
fn generate_element_code_with_context(
    element: &Element,
    error_ctx: &ErrorContext,
) -> proc_macro2::TokenStream {
    let name = &element.name;
    let name_str = quote!(#name).to_string();

    // Check if this is a component (starts with uppercase) using Symbol-based detection
    let first_char = name_str.chars().next().unwrap_or('_');
    let is_component = first_char.is_uppercase()
        && !name_str.contains("::")
        && !SUPPORTED_WIDGETS
            .iter()
            .any(|widget| widget == name_str.as_str());

    if is_component {
        // Handle component - always use VNode::component
        return generate_component_code_with_context(element, error_ctx);
    }

    // Extract the last segment of the path as a string for professional Symbol-based matching
    let widget_type = name_str.split("::").last().unwrap_or(&name_str);

    // Get attributes as key-value pairs with Symbol-based key handling
    let attributes = element.attributes.iter().map(|prop| {
        let key = &prop.key;
        let value = &prop.value;
        quote! { .#key(#value) }
    });

    // Handle different widget types using Symbol constants for professional type safety
    if widget_type == LAYOUT.as_str() {
        generate_layout_code_with_context(element, error_ctx, &attributes.collect::<Vec<_>>())
    } else if widget_type == BLOCK.as_str() {
        generate_block_code_with_context(element, error_ctx, &attributes.collect::<Vec<_>>())
    } else if widget_type == PARAGRAPH.as_str() {
        generate_paragraph_code_with_context(element, error_ctx)
    } else if widget_type == LINE.as_str() {
        generate_line_code_with_context(element, error_ctx, &attributes.collect::<Vec<_>>())
    } else if widget_type == GAUGE.as_str() {
        generate_gauge_code_with_context(element, error_ctx, &attributes.collect::<Vec<_>>())
    } else {
        generate_default_widget_code_with_context(
            element,
            error_ctx,
            &attributes.collect::<Vec<_>>(),
        )
    }
}

// Widget-specific code generation functions with error context

// Generate Layout code with error context
fn generate_layout_code_with_context(
    element: &Element,
    error_ctx: &ErrorContext,
    attributes: &[proc_macro2::TokenStream],
) -> proc_macro2::TokenStream {
    let name = &element.name;

    // Generate code for children with error context
    let children_code = element
        .children
        .iter()
        .map(|child| generate_node_code_with_context(child, error_ctx));

    // Create a widget that uses native Ratatui Layout with builder pattern
    quote! {
        VNode::Widget {
            widget: std::rc::Rc::new(rink_core::LayoutWidget::new(
                #name::default()
                    #(#attributes)*,
                vec![#(#children_code),*]
            )),
            render_fn: rink_core::create_layout_render_fn(),
            key: None,
        }
    }
}

// Generate Block code with error context
fn generate_block_code_with_context(
    element: &Element,
    error_ctx: &ErrorContext,
    attributes: &[proc_macro2::TokenStream],
) -> proc_macro2::TokenStream {
    let name = &element.name;

    if element.children.is_empty() {
        // Block without children - just render the block
        quote! {
            VNode::Widget {
                widget: std::rc::Rc::new(#name::default()
                    #(#attributes)*),
                render_fn: rink_core::create_block_render_fn(),
                key: None,
            }
        }
    } else {
        // Block with children - create a BlockWithChildren
        let children_code = element
            .children
            .iter()
            .map(|child| generate_node_code_with_context(child, error_ctx));

        quote! {
            VNode::Widget {
                widget: std::rc::Rc::new(rink_core::BlockWithChildren::new(
                    #name::default()
                        #(#attributes)*,
                    vec![#(#children_code),*]
                )),
                render_fn: rink_core::create_block_with_children_render_fn(),
                key: None,
            }
        }
    }
}

// Generate Paragraph code with error context
fn generate_paragraph_code_with_context(
    element: &Element,
    _error_ctx: &ErrorContext,
) -> proc_macro2::TokenStream {
    let name = &element.name;
    let attributes = element.attributes.iter().map(|prop| {
        let key = &prop.key;
        let value = &prop.value;
        quote! { .#key(#value) }
    });

    if element.children.is_empty() {
        // Empty paragraph
        quote! {
            VNode::Widget {
                widget: std::rc::Rc::new(#name::new("")
                    #(#attributes)*),
                render_fn: rink_core::create_paragraph_render_fn(),
                key: None,
            }
        }
    } else {
        // Simple text content from children
        let content = collect_text_content(&element.children);
        quote! {
            VNode::Widget {
                widget: std::rc::Rc::new(#name::new(#content)
                    #(#attributes)*),
                render_fn: rink_core::create_paragraph_render_fn(),
                key: None,
            }
        }
    }
}

// Generate Line code with error context
fn generate_line_code_with_context(
    element: &Element,
    _error_ctx: &ErrorContext,
    attributes: &[proc_macro2::TokenStream],
) -> proc_macro2::TokenStream {
    let content = if element.children.is_empty() {
        quote! { "" }
    } else {
        collect_text_content(&element.children)
    };

    quote! {
        VNode::Widget {
            widget: std::rc::Rc::new(ratatui::widgets::Paragraph::new(#content)
                #(#attributes)*),
            render_fn: rink_core::create_paragraph_render_fn(),
            key: None,
        }
    }
}

// Generate Gauge code with error context
fn generate_gauge_code_with_context(
    element: &Element,
    error_ctx: &ErrorContext,
    attributes: &[proc_macro2::TokenStream],
) -> proc_macro2::TokenStream {
    let name = &element.name;

    // Gauge should not have children - validate this
    if !element.children.is_empty() {
        error_ctx.add_error_with_span(
            &element.name,
            "Gauge widget should not contain children. Use attributes like ratio, percent, label, gauge_style, and use_unicode instead.",
        );
    }

    // Generate the Gauge widget with professional error handling
    quote! {
        VNode::Widget {
            widget: std::rc::Rc::new(#name::default()
                #(#attributes)*),
            render_fn: rink_core::create_gauge_render_fn(),
            key: None,
        }
    }
}

// Generate default widget code with error context
fn generate_default_widget_code_with_context(
    element: &Element,
    error_ctx: &ErrorContext,
    attributes: &[proc_macro2::TokenStream],
) -> proc_macro2::TokenStream {
    let name = &element.name;

    if element.children.is_empty() {
        quote! {
            #name::default()
                #(#attributes)*
        }
    } else if element.children.len() == 1 {
        if let Some(Node::Expression(expr)) = element.children.first() {
            // Try with new constructor for single expression
            quote! {
                #name::new(#expr)
                    #(#attributes)*
            }
        } else {
            // Default to using children method
            let child_elements = element.children.iter().map(|node| match node {
                Node::Element(child_element) => {
                    generate_element_code_with_context(child_element, error_ctx)
                }
                Node::Expression(expr) => quote! { #expr },
            });

            quote! {
                #name::default()
                    #(#attributes)*
                    .children(vec![
                        #(#child_elements),*
                    ])
            }
        }
    } else {
        // Multiple children - use children method
        let child_elements = element.children.iter().map(|node| match node {
            Node::Element(child_element) => {
                generate_element_code_with_context(child_element, error_ctx)
            }
            Node::Expression(expr) => quote! { #expr },
        });

        quote! {
            #name::default()
                #(#attributes)*
                .children(vec![
                    #(#child_elements),*
                ])
        }
    }
}

// Generate component code with error context
fn generate_component_code_with_context(
    element: &Element,
    _error_ctx: &ErrorContext,
) -> proc_macro2::TokenStream {
    generate_component_code(element)
}

// Parse implementation for Node - simplified to handle only Element and Expression
impl Parse for Node {
    fn parse(input: ParseStream) -> Result<Self> {
        if input.peek(Token![<]) {
            // Parse as Element
            Ok(Node::Element(input.parse()?))
        } else if input.peek(Brace) {
            // Parse braced expression
            let content;
            let _braces = syn::braced!(content in input);
            Ok(Node::Expression(content.parse()?))
        } else {
            // Parse as direct expression
            Ok(Node::Expression(input.parse()?))
        }
    }
}

// Parse implementation for Element
impl Parse for Element {
    fn parse(input: ParseStream) -> Result<Self> {
        // Parse opening tag
        input.parse::<Token![<]>()?;
        let name: Path = input.parse()?;

        // Parse attributes
        let mut attributes = Vec::new();
        while !input.peek(Token![>]) && !input.peek(Token![/]) {
            attributes.push(input.parse::<Prop>()?);
        }

        // Check for self-closing tag
        if input.peek(Token![/]) {
            input.parse::<Token![/]>()?;
            input.parse::<Token![>]>()?;
            return Ok(Element {
                name,
                attributes,
                children: Vec::new(),
            });
        }

        // Parse closing >
        input.parse::<Token![>]>()?;

        // Parse children
        let mut children = Vec::new();
        while !input.peek(Token![<]) || !input.peek2(Token![/]) {
            if input.is_empty() {
                break;
            }
            children.push(input.parse::<Node>()?);
        }

        // Parse closing tag
        if input.peek(Token![<]) && input.peek2(Token![/]) {
            input.parse::<Token![<]>()?;
            input.parse::<Token![/]>()?;
            let _closing_name: Path = input.parse()?;
            input.parse::<Token![>]>()?;
        }

        Ok(Element {
            name,
            attributes,
            children,
        })
    }
}

// Parse implementation for Prop
impl Parse for Prop {
    fn parse(input: ParseStream) -> Result<Self> {
        let key: Ident = input.parse()?;

        // Check for shorthand attribute (no = sign)
        if !input.peek(Token![=]) {
            // Shorthand attribute like "bold", "white", etc.
            let value = Expr::Lit(syn::ExprLit {
                attrs: vec![],
                lit: syn::Lit::Bool(syn::LitBool {
                    value: true,
                    span: key.span(),
                }),
            });
            return Ok(Prop { key, value });
        }

        input.parse::<Token![=]>()?;

        let value: Expr = if input.peek(Brace) {
            let content;
            let _braces = syn::braced!(content in input);
            content.parse()?
        } else {
            let lit: syn::Lit = input.parse()?;
            Expr::Lit(syn::ExprLit { attrs: vec![], lit })
        };

        Ok(Prop { key, value })
    }
}

// Helper function to generate code for a Component
// Creates a component instance instead of calling the function directly
fn generate_component_code(element: &Element) -> proc_macro2::TokenStream {
    let name = &element.name;
    // For now, just create a text representation of the component
    quote! {
        VNode::Text(format!("Component: {}", stringify!(#name)))
    }
}

// Helper function to collect text content from multiple nodes
fn collect_text_content(nodes: &[Node]) -> proc_macro2::TokenStream {
    let expressions: Vec<_> = nodes
        .iter()
        .filter_map(|node| match node {
            Node::Expression(expr) => Some(expr),
            _ => None,
        })
        .collect();

    if expressions.is_empty() {
        quote! { "" }
    } else if expressions.len() == 1 {
        let expr = expressions[0];
        // Check if this is a string literal and extract its value
        match expr {
            syn::Expr::Lit(syn::ExprLit {
                lit: syn::Lit::Str(lit_str),
                ..
            }) => {
                let value = &lit_str.value();
                quote! { #value }
            }
            _ => quote! { #expr },
        }
    } else {
        // Concatenate multiple expressions properly, handling string literals
        let format_args: Vec<_> = expressions
            .iter()
            .map(|expr| match expr {
                syn::Expr::Lit(syn::ExprLit {
                    lit: syn::Lit::Str(lit_str),
                    ..
                }) => {
                    let value = &lit_str.value();
                    quote! { #value }
                }
                _ => quote! { #expr },
            })
            .collect();
        quote! {
            format!("{}", vec![#(#format_args),*].join(""))
        }
    }
}
